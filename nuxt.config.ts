import fs from 'fs';

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  future: {
    compatibilityVersion: 4,
  },

  // 开启ssr服务端渲染，默认开启
  ssr: true,

  // 运行时配置
  runtimeConfig: {
    public: {
      // 客户端可访问的公共配置（会被打包到前端代码）
      baseUrl:
        process.env.NUXT_PUBLIC_API_BASE || 'https://api.sixflower.love:3000',
      ApiKey: process.env.NUXT_PUBLIC_API_KEY || '123456',
    },
  },

  // 开发服务配置
  devServer: {
    port: 1314,
    // 监听地址（默认 localhost，可自定义域名）
    host: 'web.sixflower.love',
    // 启用 HTTPS
    https: {
      key: fs.readFileSync('C:/myOpenSSL/web.sixflower.love.key').toString(),
      cert: fs.readFileSync('C:/myOpenSSL/web.sixflower.love.crt').toString(),
    },
  },

  // 模块配置
  modules: [
    '@pinia/nuxt',
    'pinia-plugin-persistedstate',
    '@vueuse/nuxt',
    '@nuxt/eslint',
    '@nuxtjs/color-mode',
    'motion-v/nuxt',
  ],

  // 构建配置
  build: {
    transpile: ['ua-parser-js', 'cropperjs'],
  },

  // 应用配置
  app: {
    // baseURL: '/v1',
    head: {
      title: '次元回廊',
      charset: 'utf-8',
      htmlAttrs: {
        lang: 'zh-CN',
      },
      meta: [{ charset: 'utf-8' }],
      link: [{ rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }],
    },
    pageTransition: { name: 'page', mode: 'out-in' },
  },

  // 全局样式配置
  css: ['~/assets/styles/css/main.css', 'cropperjs/dist/cropper.css'],

  // postcss 配置
  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {},
    },
  },

  // 全局主题颜色管理配置
  colorMode: {
    preference: 'default',
    fallback: 'default',
    storage: 'cookie',
  },
});
