<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-邮箱找回</Title>
    </Head>
    <!-- 标题 -->
    <h1>找回密码</h1>
    <!-- 账号输入框 -->
    <div class="iinput email">
      <IInput
        placeholder="请输入邮箱"
        container-class="iinput-container"
        v-model="form.email"
        @keydown.enter="retrieve"
      />
    </div>
    <!-- 密码输入框 -->
    <div class="iinput password">
      <IInput
        placeholder="请输入新密码"
        type="password"
        container-class="iinput-container"
        v-model="form.password"
        @keydown.enter="retrieve"
      />
    </div>
    <!-- 验证码输入框 -->
    <div class="iinput captcha">
      <IInput
        placeholder="请输入验证码"
        container-class="iinput-container"
        v-model="form.captcha"
        @keydown.enter="sendEmailCode"
      />
      <div class="captcha-img">
        <captcha ref="captchaRef" />
      </div>
    </div>
    <!-- 邮箱验证码输入框 -->
    <div class="iinput code">
      <IInput
        placeholder="请输入邮箱验证码"
        container-class="iinput-container"
        v-model="form.code"
        @keydown.enter="retrieve"
      />
      <div class="btn">
        <RippleButton
          class="send"
          @click="sendEmailCode"
          :disabled="sendEmailCodeLock || countdown < 60"
        >
          {{ countdown >= 60 ? '发送' : `${60 - countdown}秒` }}
        </RippleButton>
      </div>
    </div>
    <!-- 使用手机号找回链接 -->
    <div class="link">
      <NuxtLink to="/mobile/auth/retrieve/phone">使用手机号找回</NuxtLink>
    </div>

    <RippleButton class="retrieve-button" @click="retrieve">
      找回
    </RippleButton>
    <!-- 占位 -->
    <div class="placeholder"></div>
    <div class="bottom">
      <!-- 分割线加文字 -->
      <div class="divider">
        <div class="line"></div>
        <span class="divider-text">去登录</span>
        <div class="line"></div>
      </div>
      <!-- 其他登陆方式的图标 -->
      <div class="login-methods">
        <NuxtLink to="/mobile/auth/login/phone"
          ><div class="icon">
            <div class="phone">
              <IconSvg name="phone" size="2rem" color="#fff" />
            </div>
            <span>手机</span>
          </div></NuxtLink
        >
        <NuxtLink to="/mobile/auth/login/email">
          <div class="icon">
            <div class="email">
              <IconSvg name="email" size="2rem" color="#fff" />
            </div>
            <span>邮箱</span>
          </div>
        </NuxtLink>
        <NuxtLink to="/mobile/auth/login/wechat">
          <div class="icon">
            <div class="wechat">
              <IconSvg name="wechat" size="2rem" color="#fff" />
            </div>
            <span>微信</span>
          </div>
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'mobile-auth',
});

const captchaRef = ref<CaptchaExpose | null>(null);
const router = useRouter();

// 表单数据
const form = ref({
  email: '',
  password: '',
  captcha: '',
  code: '',
});

const { setEmailDate, getCodeDate } = useSendCodeStore();
// 距离上次发送邮件验证码的时间间隔

// 发送验证码倒计时
const countdown = ref(0);
const startCountdown = () => {
  countdown.value = Math.floor(
    (new Date().getTime() - new Date(getCodeDate().email).getTime()) / 1000
  );
  if (countdown.value >= 60) return;
  const timer = setInterval(() => {
    if (countdown.value > 60) {
      clearInterval(timer);
    }

    countdown.value = Math.floor(
      (new Date().getTime() - new Date(getCodeDate().email).getTime()) / 1000
    );
  }, 1000);
};
startCountdown();

// 发送邮箱验证码
// 验证码发送按钮临时封锁
const sendEmailCodeLock = ref(false);
const sendEmailCode = useDebounceFn(async () => {
  // 表单验证
  if (!form.value.email) {
    return useMessage({
      name: '邮箱未输入',
      description: '邮箱必须输入',
      type: 'error',
    });
  } else {
    const emailReg =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!emailReg.test(form.value.email)) {
      return useMessage({
        name: '邮箱格式错误',
        description: '邮箱格式错误',
        type: 'error',
      });
    }
  }
  if (!form.value.captcha) {
    return useMessage({
      name: '验证码未输入',
      description: '验证码必须输入',
      type: 'error',
    });
  }

  try {
    sendEmailCodeLock.value = true;
    await useApi().sendEmailCaptcha({
      email: form.value.email,
      code: form.value.captcha,
    });
    sendEmailCodeLock.value = false;

    // 发送成功开始倒计时
    setEmailDate();
    startCountdown();
  } catch (err) {
    captchaRef.value?.refresh();
    sendEmailCodeLock.value = false;
    throw err;
  }
}, 300);

// 找回
const retrieve = async () => {
  // 表单验证
  if (!form.value.email) {
    return useMessage({
      name: '邮箱未输入',
      description: '邮箱必须输入',
      type: 'error',
    });
  } else {
    const emailReg =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!emailReg.test(form.value.email)) {
      return useMessage({
        name: '邮箱格式错误',
        description: '邮箱格式错误',
        type: 'error',
      });
    }
  }
  if (!form.value.password) {
    return useMessage({
      name: '密码未输入',
      description: '密码必须输入',
      type: 'error',
    });
  }

  if (!checkPassword(form.value.password)) {
    return useMessage({
      name: '密码格式错误',
      description: '密码长度为6-18位，大小写字母、数字和特殊字符组合',
      type: 'error',
    });
  }
  if (!form.value.code) {
    return useMessage({
      name: '邮件验证码未输入',
      description: '邮件验证码必须输入',
      type: 'error',
    });
  }

  try {
    await useApi().retrievePassword({
      email: form.value.email,
      password: form.value.password,
      code: form.value.code,
    });
    router.push('/mobile/auth/login/email');
  } catch (err) {
    throw err;
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;

  // 标题的样式
  > h1 {
    font-size: 2rem;
    margin: 0.5rem 0 1rem 0;
    color: var(--text-primary);
  }

  // 输入框的样式
  > .iinput {
    margin-bottom: 0.5rem;

    &.captcha {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 4rem;

      * {
        height: 4rem;
      }

      > .captcha-img {
        margin-bottom: -0.4rem;
        border-radius: 1rem;
        overflow: hidden;
      }
    }

    &.code {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 4rem;

      * {
        height: 4rem;
      }

      > .btn {
        margin-bottom: -0.4rem;

        > .send {
          margin: 0 auto;
          width: 7.8rem;
          line-height: 4rem;
          font-size: 1.7rem;
          background-color: var(--button-primary);
          color: var(--text-primary);
          border-radius: 1rem;
          transition: all 0.3s ease-in-out;

          &:hover {
            background-color: var(--button-primary-hover);
          }
        }
      }
    }

    .iinput-container {
      color: var(--text-primary);

      :deep(input) {
        height: 4rem;
        line-height: 4rem;
        font-size: 2rem;
        border-radius: 1rem;
      }
    }
  }

  // 使用手机号找回链接
  > .link {
    display: flex;
    font-size: 1.3rem;
    justify-content: center;
    text-decoration: underline;
    text-decoration-color: var(--text-link-underline);
    color: var(--text-link);
    margin: 1.5rem 0 2.5rem 0;
  }

  .placeholder {
    flex: 1;
  }

  /* 分割线样式 */
  .bottom {
    width: 100%;

    > .divider {
      display: flex;
      align-items: center;
      margin: 1.5rem 0;

      > .line {
        flex: 1;
        height: 1px;
        background: rgba(255, 255, 255, 0.3); /* 半透明分割线 */
      }

      > .divider-text {
        padding: 0 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-size: 1.3rem;
      }
    }

    // 其他登陆方式区域的样式
    > .login-methods {
      display: flex;
      justify-content: space-evenly;

      .icon {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        > div {
          padding: 0.5rem;
          border-radius: 50%;

          &.phone {
            background-color: #4228c4;
          }

          &.email {
            background-color: #2196f3;
          }

          &.wechat {
            background-color: #28c445;
          }
        }

        > span {
          margin-top: 0.5rem;
          font-size: 1.3rem;
        }
      }
    }
  }

  // 找回按钮的样式
  > .retrieve-button {
    margin: 0 auto;
    width: 80vw;
    height: 5rem;
    font-size: 2rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;
    border-radius: 1.5rem;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

