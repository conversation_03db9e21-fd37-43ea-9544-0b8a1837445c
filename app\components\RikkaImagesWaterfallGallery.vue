<template>
  <div
    class="waterfall-container"
    :style="{ '--gallery-gap': gap + 'px' }"
    ref="scrollContainer"
  >
    <div class="waterfall-grid" ref="waterfallContainer">
      <div
        v-for="(col, colIndex) in waterfallColumns"
        :key="`col-${colIndex}`"
        class="waterfall-column"
        ref="columnsRef"
      >
        <slot name="item" :items="col" :column-width="actualColumnWidth">
          <AppImageCard
            v-for="item in col"
            :key="item.id"
            :item="item"
            :fixed-width="actualColumnWidth"
          />
        </slot>
      </div>
    </div>

    <!-- 预加载哨兵元素 -->
    <div
      class="preload-sentinel"
      ref="preloadSentinel"
      :style="{ height: preloadDistance + 'px' }"
    ></div>

    <div class="bottom-status" ref="bottomStatus">
      <div v-if="heightLimit === 0 && loading" class="loading-slot">
        <slot name="loading">
          <div class="default-loading">
            <div class="spinner"></div>
            <p>努力加载中....</p>
          </div>
        </slot>
      </div>

      <div v-else-if="heightLimit === 0 && !hasMore" class="end-slot">
        <slot name="end">
          <div class="default-end">
            <p>已经到底啦！</p>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 组件属性定义
const props = defineProps({
  // 图片数据数组
  items: {
    type: Array as () => PhotosWaterfallGalleryItem[],
    required: true,
    default: () => [],
  },
  // 瀑布流列数（0表示自动计算）
  columns: {
    type: Number,
    default: 0,
  },
  // 卡片基准宽度
  cardWidth: {
    type: Number,
    default: 300,
  },
  // 加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 是否还有更多数据
  hasMore: {
    type: Boolean,
    default: true,
  },
  // 卡片间距
  gap: {
    type: Number,
    default: 20,
  },
  // 预加载触发距离
  preloadDistance: {
    type: Number,
    default: 300,
  },
  // 新增：高度限制（像素）
  heightLimit: {
    type: Number,
    default: 0, // 0 表示无高度限制
  },
});

// 组件事件定义
const emit = defineEmits(['layout-updated', 'load-more']);

// 响应式变量定义
const waterfallColumns = ref<PhotosWaterfallGalleryItem[][]>([]); // 瀑布流列数据
const waterfallContainer = ref<HTMLElement | null>(null); // 容器DOM引用
const columnsRef = ref<HTMLElement[]>([]); // 列DOM引用数组
const bottomStatus = ref<HTMLElement | null>(null); // 底部状态DOM引用
const intersectionObserver = ref<IntersectionObserver | null>(null); // 交叉观察器
const actualColumnWidth = ref(0); // 实际列宽
const scrollContainer = ref<HTMLElement | null>(null); // 滚动容器引用
const preloadSentinel = ref<HTMLElement | null>(null); // 预加载哨兵元素
const columnHeights = ref<number[]>([]); // 列高度记录
const isMounted = ref(false);
const { width: containerWidth } = useElementSize(waterfallContainer);
const { width: windowWidth } = useWindowSize();
const heightLimitReached = ref(false); // 是否达到高度限制

// 计算实际列数（自动适应容器宽度）
const actualColumns = computed(() => {
  if (props.columns > 0) return props.columns;
  if (!isMounted.value) return 3;
  const totalWidth = containerWidth.value || windowWidth.value;
  if (!totalWidth) return 3;

  // 减去容器的左右padding (1rem = 16px)
  const containerPadding = 32; // 1rem * 2 (左右)
  const availableWidth = totalWidth - containerPadding;
  if (availableWidth <= 0) return 1;

  const minCardWidth = Math.max(120, props.cardWidth);

  // 考虑间距的列数计算
  // 公式：availableWidth = columns * cardWidth + (columns - 1) * gap
  // 解得：columns = (availableWidth + gap) / (cardWidth + gap)
  const maxColumns = Math.floor(
    (availableWidth + props.gap) / (minCardWidth + props.gap)
  );
  return Math.max(1, maxColumns);
});

const updateLayout = useDebounceFn(() => {
  if (!isMounted.value) return;
  const totalWidth = containerWidth.value || windowWidth.value;
  if (!totalWidth) {
    actualColumnWidth.value = props.cardWidth;
  } else {
    // 减去容器的左右padding (1rem = 16px)
    const containerPadding = 32; // 1rem * 2 (左右)
    const availableWidth = totalWidth - containerPadding;
    const totalGap = (actualColumns.value - 1) * props.gap;
    actualColumnWidth.value = Math.max(
      Math.max(120, props.cardWidth),
      (availableWidth - totalGap) / actualColumns.value
    );
  }
  const columnCount = actualColumns.value;
  if (!props.items.length) {
    waterfallColumns.value = [];
    heightLimitReached.value = false;
    return;
  }
  const columns: PhotosWaterfallGalleryItem[][] = Array.from(
    { length: columnCount },
    () => []
  );
  columnHeights.value = Array(columnCount).fill(0);
  heightLimitReached.value = false;
  for (const item of props.items) {
    let shortestColumnIndex = -1;
    let minHeight = Infinity;
    for (let i = 0; i < columnCount; i++) {
      if (
        props.heightLimit > 0 &&
        columnHeights.value[i]! >= props.heightLimit
      ) {
        continue;
      }
      if (columnHeights.value[i]! < minHeight) {
        minHeight = columnHeights.value[i]!;
        shortestColumnIndex = i;
      }
    }
    if (shortestColumnIndex === -1) {
      heightLimitReached.value = true;
      break;
    }
    const itemHeight =
      actualColumnWidth.value *
      (item.attributes.height / item.attributes.width);
    const newHeight =
      columnHeights.value[shortestColumnIndex]! +
      itemHeight +
      (columnHeights.value[shortestColumnIndex]! > 0 ? props.gap : 0);
    if (props.heightLimit > 0 && newHeight > props.heightLimit) {
      if (columnHeights.value[shortestColumnIndex]! > 0) {
        columnHeights.value[shortestColumnIndex] = props.heightLimit;
        continue;
      } else {
        columns[shortestColumnIndex]?.push(item);
        columnHeights.value[shortestColumnIndex] = itemHeight;
        heightLimitReached.value = newHeight >= props.heightLimit;
      }
    } else {
      columns[shortestColumnIndex]?.push(item);
      columnHeights.value[shortestColumnIndex] = newHeight;
    }
  }
  waterfallColumns.value = columns;
  emit('layout-updated');
}, 100);

// watch 拆分
watch(containerWidth, () => {
  if (isMounted.value) updateLayout();
});
watch(windowWidth, () => {
  if (isMounted.value) updateLayout();
});
watch(
  () => props.gap,
  () => {
    if (isMounted.value) updateLayout();
  }
);
watch(
  () => props.cardWidth,
  () => {
    if (isMounted.value) updateLayout();
  }
);
watch(
  () => props.columns,
  () => {
    if (isMounted.value) updateLayout();
  }
);
watch(
  () => props.items,
  () => {
    if (isMounted.value) updateLayout();
  },
  { deep: true }
);
watch(
  () => props.heightLimit,
  () => {
    if (isMounted.value) updateLayout();
  }
);

// IntersectionObserver 只在 items/columns/heightLimit 变化时重建
watch([() => props.items, () => props.columns, () => props.heightLimit], () => {
  nextTick(() => {
    initIntersectionObserver();
  });
});

const initIntersectionObserver = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }
  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      if (entries[0]?.isIntersecting && !props.loading && props.hasMore) {
        if (!heightLimitReached.value) {
          emit('load-more');
        }
      }
    },
    {
      root: scrollContainer.value,
      rootMargin: '0px',
      threshold: 0.01,
    }
  );
  if (preloadSentinel.value) {
    intersectionObserver.value.observe(preloadSentinel.value);
  }
};

onMounted(() => {
  isMounted.value = true;
  updateLayout();
  window.addEventListener('resize', updateLayout);
  nextTick(() => {
    initIntersectionObserver();
  });
});

onUnmounted(() => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }
  window.removeEventListener('resize', updateLayout);
});
</script>

<style scoped lang="scss">
.waterfall-container {
  --gallery-gap: 20px;
  --card-radius: 12px;
  --card-shadow: 0 10px 20px rgba(75, 0, 130, 0.15);
  --loading-color: #ffd700;
  --text-color: #e6e6fa;
  --spinner-size: 40px;

  width: 100%;
  position: relative;
  padding: 1rem;

  // 确保滚动行为不影响父级下拉刷新
  overflow: visible;
  touch-action: pan-y;
  overscroll-behavior: contain;

  .waterfall-grid {
    display: flex;
    gap: var(--gallery-gap);

    .waterfall-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--gallery-gap);
    }
  }

  .preload-sentinel {
    position: absolute;
    width: 1px;
    pointer-events: none;
    top: calc(100% - v-bind('props.preloadDistance + "px"'));
    left: 0;
    z-index: -1; /* 确保不影响布局 */
  }

  .bottom-status {
    width: 100%;
    padding: 1rem 0;
    text-align: center;

    .loading-slot,
    .end-slot {
      .default-loading,
      .default-end {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.4rem;
        color: var(--text-color);
        font-size: 0.9rem;

        .spinner {
          width: var(--spinner-size);
          height: var(--spinner-size);
          border: 3px solid rgba(255, 215, 0, 0.2);
          border-radius: 50%;
          border-top-color: var(--loading-color);
          animation: spin 1s ease-in-out infinite;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            box-shadow: 0 0 15px #ffd700;
            animation: glow 1.5s ease-in-out infinite;
          }
        }
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
</style>
