<template>
  <div
    class="waterfall-container"
    :style="{ '--gallery-gap': gap + 'px' }"
    ref="scrollContainer"
  >
    <div class="waterfall-grid" ref="waterfallContainer">
      <div
        v-for="(col, colIndex) in waterfallColumns"
        :key="`col-${colIndex}`"
        class="waterfall-column"
      >
        <slot
          name="item"
          :items="getVisibleItems(col)"
          :column-width="actualColumnWidth"
        >
          <AppPostCard
            v-for="post in getVisibleItems(col)"
            :key="post.id"
            :post="post"
            :fixed-width="actualColumnWidth"
          />
        </slot>
      </div>
    </div>

    <!-- 预加载哨兵元素 -->
    <div
      class="preload-sentinel"
      ref="preloadSentinel"
      :style="{ height: preloadDistance + 'px' }"
    ></div>

    <div class="bottom-status" ref="bottomStatus">
      <div v-if="heightLimit === 0 && loading" class="loading-slot">
        <slot name="loading">
          <div class="default-loading">
            <div class="spinner"></div>
            <p>努力加载中....</p>
          </div>
        </slot>
      </div>

      <div v-else-if="heightLimit === 0 && !hasMore" class="end-slot">
        <slot name="end">
          <div class="default-end">
            <p>已经到底啦！</p>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件props
const props = defineProps({
  // 瀑布流展示的帖子数组
  posts: {
    type: Array as () => PostsWaterfallGalleryItem[],
    required: true,
    default: () => [],
  },
  // 列数，0表示自动计算
  columns: {
    type: Number,
    default: 0,
  },
  // 卡片宽度(px)
  cardWidth: {
    type: Number,
    default: 300,
  },
  // 是否正在加载中
  loading: {
    type: Boolean,
    default: false,
  },
  // 是否还有更多数据可加载
  hasMore: {
    type: Boolean,
    default: true,
  },
  // 卡片间距(px)
  gap: {
    type: Number,
    default: 20,
  },
  // 预加载触发距离(px)
  preloadDistance: {
    type: Number,
    default: 300,
  },
  // 新增：最大高度限制(px)，0表示无限制
  heightLimit: {
    type: Number,
    default: 0,
  },
});

// 定义组件事件
const emit = defineEmits(['layout-updated', 'load-more', 'max-height-reached']);

// 响应式变量定义
const isMounted = ref(false);
const waterfallContainer = ref<HTMLElement | null>(null);
const scrollContainer = ref<HTMLElement | null>(null);
const bottomStatus = ref<HTMLElement | null>(null);
const preloadSentinel = ref<HTMLElement | null>(null);
const intersectionObserver = ref<IntersectionObserver | null>(null);
const actualColumnWidth = ref(0);
const { width: containerWidth } = useElementSize(waterfallContainer);
const { width: windowWidth } = useWindowSize();
const waterfallColumns = ref<PostsWaterfallGalleryItem[][]>([]);
const columnHeights = ref<number[]>([]);
const isMaxHeightReached = ref(false); // 是否达到最大高度限制

// 计算实际列数
const actualColumns = computed(() => {
  if (props.columns > 0) return props.columns;
  if (!isMounted.value) return 3;
  const totalWidth = containerWidth.value || windowWidth.value;
  if (!totalWidth) return 3;

  // 减去容器的左右padding (1rem = 16px)
  const containerPadding = 32; // 1rem * 2 (左右)
  const availableWidth = totalWidth - containerPadding;
  if (availableWidth <= 0) return 1;

  const minCardWidth = Math.max(120, props.cardWidth);

  // 考虑间距的列数计算
  // 公式：availableWidth = columns * cardWidth + (columns - 1) * gap
  // 解得：columns = (availableWidth + gap) / (cardWidth + gap)
  const maxColumns = Math.floor(
    (availableWidth + props.gap) / (minCardWidth + props.gap)
  );
  return Math.max(1, maxColumns);
});

// 计算卡片宽度和布局
const updateLayout = useDebounceFn(() => {
  if (!isMounted.value) return;
  const totalWidth = containerWidth.value || windowWidth.value;
  if (!totalWidth) {
    actualColumnWidth.value = props.cardWidth;
  } else {
    // 减去容器的左右padding (1rem = 16px)
    const containerPadding = 32; // 1rem * 2 (左右)
    const availableWidth = totalWidth - containerPadding;
    const totalGap = (actualColumns.value - 1) * props.gap;
    actualColumnWidth.value = Math.max(
      Math.max(120, props.cardWidth),
      (availableWidth - totalGap) / actualColumns.value
    );
  }
  const columnCount = actualColumns.value;
  if (!props.posts.length) {
    waterfallColumns.value = [];
    isMaxHeightReached.value = false;
    return;
  }
  const columns: PostsWaterfallGalleryItem[][] = Array.from(
    { length: columnCount },
    () => []
  );
  columnHeights.value = Array(columnCount).fill(0);
  isMaxHeightReached.value = false;
  const CONTENT_HEIGHT = 180;
  let skippedItems = 0;
  for (const post of props.posts) {
    let shortestColumnIndex = -1;
    let minHeight = Infinity;
    for (let i = 0; i < columnCount; i++) {
      // 跳过已满的列
      if (
        props.heightLimit > 0 &&
        columnHeights.value[i]! >= props.heightLimit
      ) {
        continue;
      }
      if (columnHeights.value[i]! < minHeight) {
        minHeight = columnHeights.value[i]!;
        shortestColumnIndex = i;
      }
    }
    // 没有可用列，说明所有列都已满
    if (shortestColumnIndex === -1) {
      isMaxHeightReached.value = true;
      break;
    }
    let postHeight = CONTENT_HEIGHT;
    if (post.cover) {
      const imageHeight =
        actualColumnWidth.value * (post.cover.height / post.cover.width);
      postHeight += imageHeight;
    }
    const newHeight =
      columnHeights.value[shortestColumnIndex]! +
      postHeight +
      (columnHeights.value[shortestColumnIndex]! > 0 ? props.gap : 0);
    if (props.heightLimit > 0 && newHeight > props.heightLimit) {
      // 如果当前列为空，必须至少渲染一个
      if (columnHeights.value[shortestColumnIndex]! === 0) {
        columns[shortestColumnIndex]?.push(post);
        columnHeights.value[shortestColumnIndex] = postHeight;
        isMaxHeightReached.value = newHeight >= props.heightLimit;
      } else {
        columnHeights.value[shortestColumnIndex] = props.heightLimit;
        skippedItems++;
        continue;
      }
    } else {
      columns[shortestColumnIndex]?.push(post);
      columnHeights.value[shortestColumnIndex] = newHeight;
    }
  }
  if (skippedItems > 0) {
    isMaxHeightReached.value = true;
    emit('max-height-reached');
  }
  waterfallColumns.value = columns;
  emit('layout-updated');
}, 100);

// watch 拆分
watch(containerWidth, () => {
  if (isMounted.value) updateLayout();
});
watch(windowWidth, () => {
  if (isMounted.value) updateLayout();
});
watch(
  () => props.gap,
  () => {
    if (isMounted.value) updateLayout();
  }
);
watch(
  () => props.cardWidth,
  () => {
    if (isMounted.value) updateLayout();
  }
);
watch(
  () => props.columns,
  () => {
    if (isMounted.value) updateLayout();
  }
);
watch(
  () => props.posts,
  () => {
    if (isMounted.value) updateLayout();
  },
  { deep: true }
);
watch(
  () => props.heightLimit,
  () => {
    if (isMounted.value) updateLayout();
  }
);

// IntersectionObserver 只在 posts/columns/heightLimit 变化时重建
watch([() => props.posts, () => props.columns, () => props.heightLimit], () => {
  nextTick(() => {
    initIntersectionObserver();
  });
});

// 获取可见项(当前直接返回所有项)
const getVisibleItems = (col: PostsWaterfallGalleryItem[]) => {
  return col;
};

// 初始化IntersectionObserver - 使用哨兵元素
const initIntersectionObserver = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }
  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      if (entries[0]?.isIntersecting && !props.loading && props.hasMore) {
        if (!isMaxHeightReached.value) {
          emit('load-more');
        }
      }
    },
    {
      root: scrollContainer.value,
      rootMargin: '0px',
      threshold: 0.01,
    }
  );
  if (preloadSentinel.value) {
    intersectionObserver.value.observe(preloadSentinel.value);
  }
};

// 组件挂载时初始化
onMounted(() => {
  isMounted.value = true;
  updateLayout();
  window.addEventListener('resize', updateLayout);
  nextTick(() => {
    initIntersectionObserver();
  });
});

// 组件卸载时清理
onUnmounted(() => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }
  window.removeEventListener('resize', updateLayout);
});
</script>

<style scoped lang="scss">
.waterfall-container {
  --gallery-gap: 20px;
  --card-radius: 12px;
  --card-shadow: 0 10px 20px rgba(75, 0, 130, 0.15);
  --loading-color: #ffd700;
  --text-color: #e6e6fa;
  --spinner-size: 40px;

  width: 100%;
  position: relative;
  padding: 1rem;

  .waterfall-grid {
    display: flex;
    gap: var(--gallery-gap);

    .waterfall-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--gallery-gap);
    }
  }

  .preload-sentinel {
    position: absolute;
    width: 1px;
    pointer-events: none;
    top: calc(100% - v-bind('props.preloadDistance + "px"'));
    left: 0;
    z-index: -1;
  }

  .bottom-status {
    width: 100%;
    padding: 1rem 0;
    text-align: center;

    .loading-slot,
    .end-slot {
      .default-loading,
      .default-end {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.4rem;
        color: var(--text-color);
        font-size: 0.9rem;

        .spinner {
          width: var(--spinner-size);
          height: var(--spinner-size);
          border: 3px solid rgba(255, 215, 0, 0.2);
          border-radius: 50%;
          border-top-color: var(--loading-color);
          animation: spin 1s ease-in-out infinite;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            box-shadow: 0 0 15px #ffd700;
            animation: glow 1.5s ease-in-out infinite;
          }
        }
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
</style>
