/**
 * 获取验证码图片
 * @param url - 验证码图片的请求地址
 * @returns 返回图片的Blob URL
 * @throws 请求失败时抛出错误
 */
const getCaptchaImg = async (url: string) => {
  try {
    // 发送GET请求获取验证码图片Blob数据
    const blob = await useRequest<Blob>(url, {
      method: 'GET',
      token: false, // 不需要认证token
      server: false, // 不通过服务端代理
    });
    // 将Blob转换为可用的URL
    return URL.createObjectURL(blob);
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 发送邮箱验证码
 * @param body - 包含邮箱地址的请求体
 * @throws 请求失败时抛出错误
 */
const sendEmailCaptcha = async (body: EmailCaptcha) => {
  try {
    // 发送POST请求到邮箱验证码接口
    await useRequest('/captcha/send-email-captcha', {
      method: 'POST',
      body, // 请求体数据
      token: false, // 不需要认证token
    });

    // 显示成功消息提示
    useMessage({
      name: '邮件发送成功',
      description: '请去邮箱查收验证码',
      type: 'success',
    });
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 发送手机验证码
 * @param body - 包含手机号的请求体
 * @throws 请求失败时抛出错误
 */
const sendMobileCaptcha = async (body: MobileCaptcha) => {
  try {
    // 发送POST请求到短信验证码接口
    await useRequest('/captcha/send-sms-captcha', {
      method: 'POST',
      body, // 请求体数据
      token: false, // 不需要认证token
    });

    // 显示成功消息提示
    useMessage({
      name: '验证码发送成功',
      description: '请去手机上查收验证码',
      type: 'success',
    });
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 导出验证码相关API
 * @returns 包含所有验证码API方法的对象
 */
export const useCaptchaApi = () => {
  return {
    /** 获取验证码图片方法 */
    getCaptchaImg,
    /** 发送邮箱验证码方法 */
    sendEmailCaptcha,
    /** 发送手机验证码方法 */
    sendMobileCaptcha,
  };
};
