<template>
  <RikkaDialog
    :show="show"
    title="图片上传"
    :show-footer="false"
    maxDodyHeight="70vh"
    @close="close"
  >
    <RikkaImageUpload
      ref="imageUpload"
      :multiple="true"
      @file-upload="handleFileUpload"
      @file-removed="clearFiles"
      @file-selected="handleFileSelected"
    />
    <RikkaImageCropper
      v-if="selectedFile"
      :imageFile="selectedFile"
      @crop-cancel="handleCropCancel"
      @crop-complete="handleCropComplete"
      style="margin: 1rem 0"
    />
    <RikkaDialog
      :show="showReleaseDialog"
      title="图片发布"
      :mask-closable="false"
      width="50rem"
      maxDodyHeight="70vh"
      @close="showReleaseDialog = false"
    >
      <!-- 发布 -->
      <div class="publish">
        <div class="image"><img :src="previewUrl" alt="预览图" /></div>
        <div class="form">
          <div class="form-item">
            <div class="lab">
              <span>分类:</span>
              <RikkaSelect
                v-model="publishPostsParams.category"
                :options="categories"
                placeholder="请选择分类"
              />
            </div>
            <div class="lab">
              <span>文件名:</span>
              <RikkaInput
                v-model="publishPostsParams.filename"
                :maxLength="30"
                placeholder="请输入文件名"
              />
            </div>
          </div>

          <div class="lab">
            <span>标签:</span>
            <RikkaTagInput
              v-model="publishPostsParams.tags"
              :max-tags="10"
              :max-tag-length="10"
              placeholder="请输入标签"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <RippleButton @click="showReleaseDialog = false" class="btn-cancel"
            >取消</RippleButton
          >
          <RippleButton
            @click="publishPhoto"
            class="btn-confirm"
            :disabled="!isPublishable"
            >发布</RippleButton
          >
        </div>
      </template>
    </RikkaDialog>
  </RikkaDialog>
</template>

<script lang="ts" setup>
const { show } = defineProps({
  show: Boolean,
});

const emit = defineEmits<{
  close: [value: boolean];
}>();
const close = () => {
  clearFiles();
  emit('close', false);
};

// 发布弹窗显示状态
const showReleaseDialog = ref(false);
const imageUpload = ref<ImageUploadExpose | null>(null); // 图片上传组件实例
// 原图
const originalFiles = ref<File[] | null>(null); // 上传的原图文件数组
const selectedFile = ref<File | null>(null); // 选中的文件对象
const selectedFileIndex = ref(0); // 选中的文件索引
const previewUrl = ref(''); // 预览图片链接

// 清空文件
const clearFiles = () => {
  originalFiles.value = null;
  selectedFile.value = null;
  selectedFileIndex.value = 0;
};

/**
 * 处理上传文件变化事件
 * @param files - 上传的文件数组
 */
const handleFileUpload = (files: File[]) => {
  if (files && files.length > 0) {
    originalFiles.value = files;
  }
};

/**
 * 处理文件选中事件
 * @param file - 选中的文件对象
 * @param index - 选中文件的索引
 */
const handleFileSelected = (file: File | null, index: number) => {
  selectedFile.value = file;
  selectedFileIndex.value = index;

  // 自动设置文件名（去掉扩展名），在裁剪前就设置好
  if (file && !publishPostsParams.value.filename) {
    const filename = file.name.replace(/\.[^/.]+$/, '');
    publishPostsParams.value.filename = filename;
  }
};

/**
 * 处理图片裁剪完成事件
 * @param file - 裁剪后的图片对象
 */
const handleCropComplete = (file: File) => {
  selectedFile.value = file;
  previewImage(file);
};

/**
 * 处理图片裁剪取消事件
 */
const handleCropCancel = () => {
  previewImage(selectedFile.value!);
};

/**
 * 待发布图片预览
 * @param file - 要预览的图片对象
 */
const previewImage = (file: File) => {
  previewUrl.value = URL.createObjectURL(file);
  showReleaseDialog.value = true;
};

/**
 * 发布图片
 */
const categories: Array<{ label: string; value: PhotoCategory }> = [
  {
    label: '美女',
    value: '美女',
  },
  {
    label: '动漫',
    value: '动漫',
  },
  {
    label: '城市',
    value: '城市',
  },
  {
    label: '风景',
    value: '风景',
  },
  {
    label: '二次元',
    value: '二次元',
  },
  {
    label: '美食',
    value: '美食',
  },
  {
    label: '其他',
    value: '其他',
  },
]; // 分类选项
const publishPostsParams = ref<{
  category: PhotoCategory;
  tags: Array<string>;
  filename: string;
}>({
  category: '其他',
  tags: [],
  filename: '',
});
// 发布按钮是否可用
const isPublishable = computed(() => {
  return (
    publishPostsParams.value.category &&
    publishPostsParams.value.tags.length > 0 &&
    publishPostsParams.value.tags.length <= 10 &&
    publishPostsParams.value.filename.length > 0
  );
});
// 清空发布参数
const clearPublishParams = () => {
  // 重置发布参数为默认值
  publishPostsParams.value = {
    category: '其他',
    tags: [],
    filename: '',
  };
};
// 发布图片
const publishPhoto = async () => {
  // 发布图片的逻辑
  try {
    // 开始加载动画
    useLoading().start({
      title: '发布中',
      description: '图片正在发布中，请稍候...',
    });
    // 上传图片
    const data = await useApi().uploadPhoto(selectedFile.value!);
    // 如果上传失败，结束加载动画并返回
    if (!data || !data[0]) return;
    // 发布图片
    await useApi().publishPhoto({
      ...data[0],
      ...publishPostsParams.value,
      status: 'approved',
    });
    // 结束加载动画
    useLoading().stop();
    // 显示发布成功的消息
    useMessage({
      name: '发布成功',
      description: '图片已发布',
      type: 'success',
    });
    // 重置响应式变量
    showReleaseDialog.value = false;
    // 移除已上传文件
    imageUpload.value?.removeFile(selectedFileIndex.value);
    // 清空tags、分类、文件名
    clearPublishParams();
  } catch (err) {
    // 发生错误时停止加载动画
    useLoading().stop();
    // 显示发布失败的消息
    useMessage({
      name: '发布失败',
      description: '图片发布失败，请稍后再试',
      type: 'error',
    });
  }
};
</script>

<style lang="scss" scoped>
.publish {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  > .image {
    display: flex;
    height: 20rem;

    align-items: center;
    justify-content: center;
    margin-right: 1rem;

    > img {
      height: 100%;
    }
  }

  > .form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;
    width: 40rem;

    > .form-item {
      width: 100%;
      display: flex;

      > :first-child {
        flex: 1;
      }

      > :last-child {
        flex: 2;
        margin-left: 1rem;
      }
    }

    .lab {
      width: 100%;
      margin-top: 1rem;
      display: flex;
      align-items: center;

      &:first-child {
        width: 15rem;
      }

      > span {
        margin-right: 2rem;
        font-size: 1.2rem;
      }

      > div {
        flex: 1;
      }
    }
  }
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-cancel-hover);
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

