<template>
  <div class="rikka-tag-container">
    <div class="rune-decoration">✪</div>
    <div
      class="tag-input-wrapper"
      :class="{ 'has-tags': tags.length > 0 }"
      @click="focusInput"
    >
      <div ref="scrollContainerRef" class="scrollable-container">
        <transition-group name="tag" tag="div" class="tags-wrapper">
          <span
            v-for="(tag, index) in visibleTags"
            :key="tag"
            class="tag"
            :style="{ animationDelay: `${index * 0.05}s` }"
          >
            <span class="tag-content" :title="tag">{{ truncateTag(tag) }}</span>
            <span
              class="remove-tag"
              @click.stop="removeTag(getActualIndex(index))"
              >×</span
            >
          </span>
        </transition-group>
        <input
          ref="inputRef"
          v-model="inputValue"
          class="tag-input"
          type="text"
          :placeholder="placeholder"
          @keydown="handleKeyDown"
          @blur="addTagFromInput"
        />
      </div>
    </div>
    <div
      class="counter"
      :class="{
        warning: tags.length >= maxTags * 0.8,
        danger: tags.length >= maxTags,
      }"
    >
      {{ tags.length }}/{{ maxTags }}
    </div>
  </div>
</template>

<script setup lang="ts">
// 定义组件属性
const props = defineProps({
  modelValue: {
    // 双向绑定的标签数组
    type: Array as () => string[],
    default: () => [],
  },
  maxTags: {
    // 最大标签数量
    type: Number,
    default: 2,
  },
  maxTagLength: {
    // 单个标签最大长度
    type: Number,
    default: 10,
  },
  placeholder: {
    // 输入框占位文本
    type: String,
    default: '输入后按回车添加标签...',
  },
  exampleTags: {
    // 示例标签数组
    type: Array as () => string[],
    default: () => [],
  },
  // 控制显示的标签数量
  visibleTagsNumber: {
    type: Number,
    default: 4,
  },
});

// 定义组件事件
const emit = defineEmits(['update:modelValue']);

// 响应式状态
const tags = ref<string[]>([]); // 存储标签的数组
const inputValue = ref(''); // 输入框的值
const inputRef = ref<HTMLInputElement | null>(null); // 输入框DOM引用
const scrollContainerRef = ref<HTMLElement | null>(null); // 滚动容器DOM引用

// 计算属性：获取可见的标签（默认显示最后几个）
const visibleTags = computed(() => {
  const startIndex = Math.max(0, tags.value.length - props.visibleTagsNumber);
  return tags.value.slice(startIndex);
});

// 根据可见索引获取实际标签索引
const getActualIndex = (visibleIndex: number) => {
  const startIndex = Math.max(0, tags.value.length - props.visibleTagsNumber);
  return startIndex + visibleIndex;
};

// 监听外部传入的modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    tags.value = [...newVal];
  },
  { immediate: true }
);

// 组件挂载时添加示例标签
onMounted(() => {
  if (props.exampleTags.length > 0) {
    setTimeout(() => {
      props.exampleTags.forEach((tag) => {
        if (tags.value.length < props.maxTags) {
          tags.value.push(tag);
        }
      });
      emit('update:modelValue', [...tags.value]);
    }, 300);
  }
});

// 标签文本截断处理
const truncateTag = (tag: string) => {
  const maxLength = props.maxTagLength > 2 ? 2 : props.maxTagLength;
  return tag.length > maxLength ? `${tag.substring(0, maxLength)}...` : tag;
};

// 添加新标签
const addTag = (tagText: string) => {
  if (!tagText || tags.value.length >= props.maxTags) return;

  const trimmedText = tagText.trim();
  if (!trimmedText) return;

  // 处理标签长度限制
  const finalText =
    trimmedText.length > props.maxTagLength
      ? trimmedText.substring(0, props.maxTagLength)
      : trimmedText;

  // 防止重复标签
  if (!tags.value.includes(finalText)) {
    tags.value.push(finalText);
    emit('update:modelValue', [...tags.value]);

    // 滚动到最右边显示新标签
    nextTick(() => {
      if (scrollContainerRef.value) {
        scrollContainerRef.value.scrollLeft =
          scrollContainerRef.value.scrollWidth;
      }
    });
  }
};

// 移除指定索引的标签
const removeTag = (index: number) => {
  if (index >= 0 && index < tags.value.length) {
    tags.value.splice(index, 1);
    emit('update:modelValue', [...tags.value]);
    nextTick(() => {
      if (inputRef.value) {
        inputRef.value.focus();
      }
    });
  }
};

// 从输入框添加标签
const addTagFromInput = () => {
  if (inputValue.value.trim()) {
    addTag(inputValue.value);
    inputValue.value = '';
  }
};

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' || e.key === ',') {
    e.preventDefault();
    addTagFromInput();
  } else if (
    e.key === 'Backspace' &&
    inputValue.value === '' &&
    tags.value.length > 0
  ) {
    removeTag(tags.value.length - 1);
  }
};

// 聚焦输入框
const focusInput = () => {
  if (inputRef.value) {
    inputRef.value.focus();
  }
};
</script>

<style lang="scss" scoped>
.rikka-tag-container {
  position: relative;
}

.rune-decoration {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: -0.7rem;
  color: var(--input-rune-color);
  font-size: 1.8rem;
  opacity: 0.7;
  text-shadow: var(--input-rune-glow);
  transition: all 0.3s ease;
  animation: glow 3s infinite;
  z-index: 1;

  .rikka-tag-container:hover & {
    opacity: 1;
    text-shadow: 0 0 0.9375rem rgba(255, 105, 180, 0.8);
  }
}

.tag-input-wrapper {
  position: relative;
  padding: 0.2rem 0.7rem;
  background: var(--input-field-bg);
  border: var(--input-field-border);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
  box-shadow:
    0 0 0.625rem rgba(75, 0, 130, 0.3),
    inset 0 0 0.3125rem rgba(143, 51, 255, 0.2);
  display: block;
  cursor: text;

  &:focus-within {
    border: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    background: rgba(35, 15, 65, 0.9);
  }
}

.scrollable-container {
  display: flex;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
  min-height: 2.8571rem;
  align-items: center;

  &::-webkit-scrollbar {
    height: 0.4286rem;
  }

  &::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: 0.2143rem;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 0.2143rem;
  }
}

.tags-wrapper {
  display: flex;
  flex-wrap: nowrap;
  gap: 0.5714rem;
  flex-shrink: 0;
}

.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.2rem 0.1rem;
  background: var(--tag-bg);
  border: var(--tag-border);
  border-radius: var(--border-radius-sm);
  color: var(--tag-text);
  font-size: 0.7rem;
  animation: tagAppear 0.3s ease-out;
  transition: all 0.2s ease;
  box-shadow: var(--tag-shadow);
  max-width: 12.8571rem;
  white-space: nowrap;
  flex-shrink: 0;

  &:hover {
    background: var(--tag-hover-bg);
    transform: translateY(-0.1429rem);
    box-shadow: 0 0.2857rem 0.7143rem rgba(0, 0, 0, 0.4);
  }
}

.tag-content {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 10.7143rem;
  display: block;
}

.remove-tag {
  cursor: pointer;
  color: var(--tag-remove-color);
  font-size: 1.1rem;
  transition: transform 0.2s ease;
  width: 1.2857rem;
  height: 1.2857rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(210, 15, 57, 0.1);

  &:hover {
    transform: scale(1.2);
    background: rgba(210, 15, 57, 0.2);
  }
}

.tag-input {
  flex: 0 0 auto;
  min-width: 8.5714rem;
  height: 2.5rem;
  background: transparent;
  border: none;
  outline: none;
  color: var(--text-primary);
  font-size: 0.95rem;
  padding: 0 0.5714rem;

  &::placeholder {
    color: var(--input-placeholder);
    font-size: 0.9rem;
  }
}

.counter {
  position: absolute;
  right: 0;
  bottom: 50%;
  transform: translateY(50%);
  font-size: 0.85rem;
  padding: 0.2rem 0.6rem;
  border-radius: var(--border-radius-sm);
  background: var(--counter-bg);
  color: var(--counter-text);
  transition: all 0.3s ease;
  z-index: 1;

  &.warning {
    background: var(--counter-warning-bg);
    color: var(--counter-warning-text);
    text-shadow: 0 0 0.1875rem rgba(255, 215, 0, 0.3);
  }

  &.danger {
    background: var(--counter-danger-bg);
    color: var(--counter-danger-text);
    text-shadow: 0 0 0.1875rem rgba(210, 15, 57, 0.3);
  }
}

// 标签动画
.tag-enter-active,
.tag-leave-active {
  transition: all 0.3s ease;
}

.tag-enter-from,
.tag-leave-to {
  opacity: 0;
  transform: translateY(0.7143rem) scale(0.8);
}

.tag-leave-active {
  position: absolute;
}
</style>

