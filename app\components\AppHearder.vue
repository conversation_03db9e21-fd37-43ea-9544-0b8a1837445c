<template>
  <header class="header">
    <div class="left">
      <slot name="left" />
    </div>
    <div class="center">
      <!-- 搜索框 -->
      <slot>
        <VanishingInput
          v-model="text"
          :placeholders="placeholders"
          @submit="handleSearchSubmit"
        >
          <template #submit-icon>
            <IconSvg name="send" size="1.5rem" color="var(--text-secondary)" />
          </template>
        </VanishingInput>
      </slot>
    </div>
    <div class="right">
      <div>
        <div
          :class="{
            'icon-button': true,
            'upload-image': true,
            disabled: !authStore.isAuthenticated,
          }"
          @click="uploadImageDialogFlag = authStore.isAuthenticated"
        >
          <IconSvg
            name="uploadImage"
            size="1.5rem"
            color="var(--text-secondary)"
          />
          <span>发图</span>
        </div>
        <div
          :class="{
            'icon-button': true,
            'write-post': true,
            disabled: !authStore.isAuthenticated,
          }"
          @click="writePostDialogFlag = authStore.isAuthenticated"
        >
          <IconSvg
            name="writePost"
            size="1.5rem"
            color="var(--text-secondary)"
          />
          <span>发帖</span>
        </div>
        <div
          :class="{
            'icon-button': true,
            notification: true,
          }"
          @click="notificationDialogFlag = authStore.isAuthenticated"
        >
          <IconSvg
            name="notification"
            size="1.5rem"
            color="var(--text-secondary)"
          />
          <span>通知</span>
        </div>
        <NuxtLink to="/mine" v-if="authStore.isAuthenticated">
          <img
            :src="
              authStore.getAuth()?.avatar ||
              'https://file.sixflower.top/images/original-default_avatar.png'
            "
          />
        </NuxtLink>
        <div v-else>
          <RippleButton
            class="login-button"
            @click="() => router.push('/auth/login/email')"
          >
            去登录
          </RippleButton>
        </div>
      </div>
    </div>
    <!-- 上传图片弹窗 -->
    <AppImageUploadModal
      :show="uploadImageDialogFlag"
      @close="uploadImageDialogFlag = false"
    />
    <!-- 写文章弹窗 -->
    <AppPostWriteModel
      :show="writePostDialogFlag"
      @close="writePostDialogFlag = false"
    />
    <!-- 通知弹窗 -->
    <AppNotificationModel
      :show="notificationDialogFlag"
      @close="notificationDialogFlag = false"
    />
  </header>
</template>

<script lang="ts" setup>
// 引入路由状态管理
const routerStore = useRouterStore();
// 引入认证状态管理
const authStore = useAuthStore();

// 引入路由对象
const router = useRouter();

// 尝试获取用户信息
const getUserInfo = () => {
  try {
    useApi().getMyInfo();
  } catch (err) {}
};
getUserInfo();

// 搜索框占位符
const placeholders = ref(['六花']);
// 搜索框文本
const text = ref('');

// 请求获取搜索框占位符
const getSearchPlaceholders = async () => {
  try {
    placeholders.value = await useApi().getSearchHotwords();
    // 如果获取的占位符为空，则使用默认占位符
    if (placeholders.value.length === 0) {
      placeholders.value = ['六花'];
    }
  } catch (err) {}
};
getSearchPlaceholders();

// 搜索框提交事件处理函数
const handleSearchSubmit = () => {
  if (!routerStore.get()?.includes('search')) {
    router.push({ path: '/search/images', query: { key: text.value } });
    return;
  }

  router.push({ path: routerStore.get(), query: { key: text.value } }); // 更换搜索字符串
};

// 上传图片弹窗显示标志
const uploadImageDialogFlag = ref(false);

// 写文章弹窗显示标志
const writePostDialogFlag = ref(false);

// 通知弹窗显示标志
const notificationDialogFlag = ref(false);
</script>

<style lang="scss" scoped>
/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;

  > .left {
    flex: 1;
  }

  > .center {
    width: 30rem;
    margin: 0 1rem;
  }

  > .right {
    flex: 1;

    overflow: hidden;

    > :first-child {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      a {
        margin-left: 1rem;

        > img {
          width: 3rem;
          height: 3rem;
          border-radius: 50%;
          object-fit: cover;
          object-position: center;
          transition: var(--transition);
        }
      }

      // 登录按钮的样式
      .login-button {
        width: 8rem;
        background-color: var(--button-primary);
        color: var(--text-primary);
        margin-left: 1rem;
        transition: all 0.3s ease-in-out;

        &:hover {
          background-color: var(--button-primary-hover);
        }
      }

      .icon-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 0.7rem;
        padding: 0.1rem 0.5rem;
        border-radius: 0.5rem;

        &:hover {
          background-color: var(--background-floating);
        }

        &.disabled {
          cursor: not-allowed;
          opacity: 0.5;
        }
      }
    }
  }
}
</style>

