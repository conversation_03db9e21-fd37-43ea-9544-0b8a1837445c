/**
 * 图片基础属性信息
 * @property {number} size - 图片大小（字节）
 * @property {number} width - 图片宽度（像素）
 * @property {number} height - 图片高度（像素）
 * @property {string} format - 图片格式（如：jpg/png/webp等）
 */
interface Attribute {
  size: number;
  width: number;
  height: number;
  format: string;
}

/**
 * 图片上传信息
 * @property {string} filename - 原始文件名
 * @property {string} downloadFilename - 下载时使用的文件名
 * @property {string} url - 图片访问URL
 * @property {PhotoCategory} category - 图片分类
 * @property {string[]} tags - 图片标签数组
 * @property {Attribute} attributes - 图片属性信息
 * @property {string} [status] - 可选，图片状态（如：审核中/已发布等）
 */
interface photoUploadInfo {
  filename: string;
  downloadFilename: string;
  url: string;
  category: PhotoCategory;
  tags: string[];
  attributes: Attribute;
  status?: string;
}

/**
 * 用户自己上传的图片详情
 * @extends photoUploadInfo
 * @property {string} id - 图片唯一ID
 * @property {number} favoriteCount - 收藏数
 * @property {number} likeCount - 点赞数
 * @property {number} viewCount - 浏览数
 * @property {number} downloadCount - 下载数
 * @property {string} status - 图片状态（必填）
 */
interface GetSelfPhotosInfo {
  id: string;
  filename: string;
  url: string;
  category: PhotoCategory;
  tags: string[];
  favoriteCount: number;
  likeCount: number;
  viewCount: number;
  isLiked: boolean;
  isFavorited: boolean;
  downloadCount: number;
  attributes: Attribute;
  status: string;
}

/**
 * 其他用户上传的图片详情
 * @property {string} id - 图片唯一ID
 * @property {string} filename - 文件名
 * @property {Object} user - 上传用户信息
 * @property {string} user.uid - 用户ID
 * @property {string} user.nickname - 用户昵称
 * @property {string} user.avatar - 用户头像URL
 * @property {string} url - 图片访问URL
 * @property {string} category - 图片分类
 * @property {string[]} tags - 图片标签数组
 * @property {number} favoriteCount - 收藏数
 * @property {number} likeCount - 点赞数
 * @property {boolean} isLiked - 当前用户是否点赞
 * @property {boolean} isFavorited - 当前用户是否收藏
 * @property {number} downloadCount - 下载数
 * @property {number} viewCount - 浏览数
 * @property {Attribute} attributes - 图片属性信息
 */
interface GetOtherPhotosInfo {
  id: string;
  filename: string;
  user: {
    uid: string;
    nickname: string;
    avatar: string;
  };
  url: string;
  category: string;
  tags: string[];
  favoriteCount: number;
  likeCount: number;
  viewCount?: number;
  downloadCount: number;
  isLiked: boolean;
  isFavorited: boolean;
  attributes: Attribute;
}

/**
 * 图片列表项
 * @property {string} id - 图片唯一ID
 * @property {string} filename - 文件名
 * @property {string} url - 图片访问URL
 * @property {string[]} tags - 图片标签数组
 * @property {number} favoriteCount - 收藏数
 * @property {number} likeCount - 点赞数
 * @property {boolean} isLiked - 当前用户是否点赞
 * @property {boolean} isFavorited - 当前用户是否收藏
 * @property {number} downloadCount - 下载数
 * @property {Attribute} attributes - 图片属性信息
 */
interface PhotosList {
  id: string;
  filename: string;
  url: string;
  tags: string[];
  favoriteCount: number;
  likeCount: number;
  isLiked: boolean;
  isFavorited: boolean;
  downloadCount: number;
  attributes: Attribute;
}

/**
 * 图片分类枚举
 * - '美女': 人物写真类图片
 * - '动漫': 动漫插画类图片
 * - '风景': 自然风景类图片
 * - '城市': 城市建筑类图片
 * - '自然': 自然生态类图片
 * - '美食': 食物餐饮类图片
 * - '二次元': 二次元风格图片
 * - '其他': 未分类图片
 */
type PhotoCategory =
  | '美女'
  | '动漫'
  | '风景'
  | '城市'
  | '自然'
  | '美食'
  | '二次元'
  | '其他';

/**
 * 图片排序字段
 * - 'createTime': 按创建时间排序
 * - 'updateTime': 按更新时间排序
 * - 'downloadCount': 按下载量排序
 * - 'viewCount': 按浏览量排序
 * - 'likeCount': 按点赞数排序
 * - 'favoriteCount': 按收藏数排序
 */
type PhotosSortField =
  | 'createTime'
  | 'updateTime'
  | 'downloadCount'
  | 'viewCount'
  | 'likeCount'
  | 'favoriteCount';

/**
 * 图片列表查询参数
 * @extends QueryTemplate<PhotosSortField>
 * @property {string} [category] - 可选，按分类筛选
 * @property {string[]} [tags] - 可选，按标签筛选
 */
type PhotosListQuery = QueryTemplate<PhotosSortField> & {
  category?: string;
  tags?: string[];
};
