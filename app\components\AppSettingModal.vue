<template>
  <RikkaDialog
    :show="show"
    title="隐私设置"
    :width="dialogWidth"
    @close="close"
  >
    <!-- 优化后的用户设置对话框内容区域 -->
    <div class="user-setting-modal">
      <!-- 设置说明 -->
      <div class="setting-description">
        <p>控制其他用户可以查看您的哪些信息和内容</p>
      </div>

      <!-- 设置选项分组 -->
      <div class="setting-groups">
        <!-- 第一组：个人信息公开设置 -->
        <div class="setting-group">
          <div class="group-header">
            <IconSvg
              name="mine"
              size="1.2rem"
              color="var(--interactive-primary)"
            />
            <h3>个人信息</h3>
          </div>
          <div class="group-content">
            <div class="setting-item">
              <RikkaRadio v-model="settings.showFollowList">
                <div class="setting-label">
                  <span class="label-text">公开我的关注列表</span>
                  <span class="label-desc">其他用户可以查看我关注的人</span>
                </div>
              </RikkaRadio>
            </div>
            <div class="setting-item">
              <RikkaRadio v-model="settings.showFansList">
                <div class="setting-label">
                  <span class="label-text">公开我的粉丝列表</span>
                  <span class="label-desc">其他用户可以查看我的粉丝</span>
                </div>
              </RikkaRadio>
            </div>
          </div>
        </div>

        <!-- 第二组：内容公开设置 -->
        <div class="setting-group">
          <div class="group-header">
            <IconSvg
              name="image"
              size="1.2rem"
              color="var(--interactive-primary)"
            />
            <h3>我的内容</h3>
          </div>
          <div class="group-content">
            <div class="setting-item">
              <RikkaRadio v-model="settings.showMyContentList">
                <div class="setting-label">
                  <span class="label-text">公开我的帖子列表</span>
                  <span class="label-desc">其他用户可以查看我发布的帖子</span>
                </div>
              </RikkaRadio>
            </div>
            <div class="setting-item">
              <RikkaRadio v-model="settings.showMyPhotoList">
                <div class="setting-label">
                  <span class="label-text">公开我的照片列表</span>
                  <span class="label-desc">其他用户可以查看我上传的照片</span>
                </div>
              </RikkaRadio>
            </div>
          </div>
        </div>

        <!-- 第三组：互动记录设置 -->
        <div class="setting-group">
          <div class="group-header">
            <IconSvg
              name="like"
              size="1.2rem"
              color="var(--interactive-primary)"
            />
            <h3>互动记录</h3>
          </div>
          <div class="group-content">
            <div class="setting-item">
              <RikkaRadio v-model="settings.showFavoriteContentList">
                <div class="setting-label">
                  <span class="label-text">公开收藏帖子列表</span>
                  <span class="label-desc">其他用户可以查看我收藏的帖子</span>
                </div>
              </RikkaRadio>
            </div>
            <div class="setting-item">
              <RikkaRadio v-model="settings.showLikeContentList">
                <div class="setting-label">
                  <span class="label-text">公开喜欢帖子列表</span>
                  <span class="label-desc">其他用户可以查看我喜欢的帖子</span>
                </div>
              </RikkaRadio>
            </div>
            <div class="setting-item">
              <RikkaRadio v-model="settings.showFavoritePhotoList">
                <div class="setting-label">
                  <span class="label-text">公开收藏照片列表</span>
                  <span class="label-desc">其他用户可以查看我收藏的照片</span>
                </div>
              </RikkaRadio>
            </div>
            <div class="setting-item">
              <RikkaRadio v-model="settings.showLikePhotoList">
                <div class="setting-label">
                  <span class="label-text">公开喜欢照片列表</span>
                  <span class="label-desc">其他用户可以查看我喜欢的照片</span>
                </div>
              </RikkaRadio>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 对话框底部按钮区域 -->
    <template #footer>
      <div class="dialog-footer">
        <!-- 取消按钮：点击后关闭对话框 -->
        <RippleButton class="btn-cancel" @click="close()">取消</RippleButton>
        <!-- 确定按钮：点击后保存设置并关闭对话框；如果用户未登录，则按钮禁用 -->
        <RippleButton
          class="btn-confirm"
          @click="saveSetting"
          :disabled="!authStore.getIsLogin()"
          >确定</RippleButton
        >
      </div>
    </template>
  </RikkaDialog>
</template>

<script lang="ts" setup>
// 定义组件接收的 props
const { show } = defineProps({
  show: Boolean,
});

// 使用 authStore 管理用户认证状态
const authStore = useAuthStore();

// 响应式设计 - 动态计算对话框宽度
const windowWidth = ref(0);
const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '90vw' : '45rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});
// 定义组件触发的事件
const emit = defineEmits<{
  close: [value: boolean];
}>();
// 关闭对话框的方法，可选参数 flag 用于区别保存或取消关闭
const close = (flag = false) => {
  emit('close', flag);
};

// 定义用户设置的数据模型，默认值为 false
const settings = ref<UserSettings>({
  showFollowList: false,
  showFansList: false,
  showFavoritePhotoList: false,
  showLikePhotoList: false,
  showFavoriteContentList: false,
  showLikeContentList: false,
  showMyPhotoList: false,
  showMyContentList: false,
});

// 异步获取用户当前设置的方法
const getSetting = async () => {
  settings.value = await useApi().getUserSetting();
};

// 异步保存用户设置的方法
const saveSetting = async () => {
  await useApi().setMySetting(settings.value);
  close();
};

// 监听 show 属性变化，如果对话框显示且用户已登录，则获取用户设置
watch(
  () => show,
  (val) => {
    if (val && authStore.getIsLogin()) {
      getSetting();
    }
  }
);
</script>

<style lang="scss" scoped>
// 优化后的用户设置对话框样式
.user-setting-modal {
  .setting-description {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background-color: var(--background-floating);
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);

    p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
      line-height: 1.5;
    }
  }

  .setting-groups {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    .setting-group {
      background-color: var(--background-floating);
      border-radius: 1rem;
      padding: 1.5rem;
      border: 1px solid var(--border-color);
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--interactive-primary);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .group-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1.25rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid var(--border-color);

        h3 {
          margin: 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--text-primary);
        }
      }

      .group-content {
        .setting-item {
          margin-bottom: 1rem;

          &:last-child {
            margin-bottom: 0;
          }

          .setting-label {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;

            .label-text {
              font-size: 1rem;
              font-weight: 500;
              color: var(--text-primary);
            }

            .label-desc {
              font-size: 0.85rem;
              color: var(--text-secondary);
              line-height: 1.4;
            }
          }
        }
      }
    }
  }
}

// 优化后的对话框底部按钮样式
.dialog-footer {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color);

  .btn-cancel,
  .btn-confirm {
    padding: 0.75rem 2rem;
    border-radius: 0.75rem;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    min-width: 6rem;
  }

  .btn-cancel {
    background-color: var(--background-floating);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);

    &:hover {
      background-color: var(--background-hover);
      color: var(--text-primary);
      border-color: var(--interactive-primary);
    }
  }

  .btn-confirm {
    background-color: var(--interactive-primary);
    color: white;

    &:hover {
      background-color: var(--interactive-primary-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    &:disabled {
      background-color: var(--background-disabled);
      color: var(--text-disabled);
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }
}

// 移动端响应式优化
@media (max-width: 768px) {
  .user-setting-modal {
    .setting-description {
      padding: 0.75rem;
      margin-bottom: 1.5rem;

      p {
        font-size: 0.85rem;
      }
    }

    .setting-groups {
      gap: 1rem;

      .setting-group {
        padding: 1rem;

        .group-header {
          margin-bottom: 1rem;

          h3 {
            font-size: 1rem;
          }
        }

        .group-content {
          .setting-item {
            margin-bottom: 0.75rem;

            .setting-label {
              .label-text {
                font-size: 0.9rem;
              }

              .label-desc {
                font-size: 0.8rem;
              }
            }
          }
        }
      }
    }
  }

  .dialog-footer {
    padding-top: 1rem;
    gap: 0.75rem;

    .btn-cancel,
    .btn-confirm {
      padding: 0.75rem 1.5rem;
      font-size: 0.9rem;
      min-width: 5rem;
    }
  }
}
</style>

