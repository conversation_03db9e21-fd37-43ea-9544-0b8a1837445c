<template>
  <div>
    <RikkaDialog :show="show" title="发布帖子" width="40rem" @close="close">
      <div class="publish">
        <div class="form">
          <div class="form-item">
            <div class="lab">
              <span>标题:</span>
              <RikkaInput
                v-model="publishPostsParams.title"
                :maxLength="30"
                placeholder="请输入标题"
              />
            </div>
            <div class="lab">
              <span>分类:</span>
              <RikkaSelect
                v-model="publishPostsParams.category"
                :options="categories"
                placeholder="请选择分类"
              />
            </div>
          </div>

          <div class="lab">
            <span>标签:</span>
            <RikkaTagInput
              v-model="publishPostsParams.tags"
              :max-tags="10"
              :max-tag-length="10"
              placeholder="请输入标签"
            />
          </div>
          <div class="lab">
            <span>内容:</span>
            <RikkaInput
              v-model="publishPostsParams.content"
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
            />
          </div>
        </div>
        <!-- 在对话框内显示一个居中的提示信息 -->
        <div class="posts-image-selector">
          <AppImageSelector
            :images="userImages"
            v-model="publishPostsParams.photos"
            :has-more="hasMore"
            :loading="loading"
            @load-more="loadMoreImages"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <RippleButton @click="close()" class="btn-cancel">取消</RippleButton>
          <RippleButton
            @click="publishPosts"
            class="btn-confirm"
            :disabled="!isPublishable"
            >发布</RippleButton
          >
        </div>
      </template>
    </RikkaDialog>
  </div>
</template>

<script lang="ts" setup>
// 定义组件的 props
const { show } = defineProps({
  show: Boolean,
});

// 定义组件的 emits
const emit = defineEmits<{
  close: [value: boolean];
}>();

// 关闭对话框并清空发布参数
const close = (flag = false) => {
  clearPublishParams();
  emit('close', flag);
};

const authStore = useAuthStore();

// 分类选项
const categories: Array<{ label: string; value: PhotoCategory }> = [
  {
    label: '美女',
    value: '美女',
  },
  {
    label: '动漫',
    value: '动漫',
  },
  {
    label: '城市',
    value: '城市',
  },
  {
    label: '风景',
    value: '风景',
  },
  {
    label: '二次元',
    value: '二次元',
  },
  {
    label: '美食',
    value: '美食',
  },
  {
    label: '其他',
    value: '其他',
  },
];

// 发布帖子的参数
const publishPostsParams = ref<PostsUploadInfo>({
  title: '',
  content: '',
  category: '其他',
  tags: [],
  photos: [],
  status: 'approved',
});

// 判断发布按钮是否可用
const isPublishable = computed(() => {
  return (
    publishPostsParams.value.category &&
    publishPostsParams.value.tags.length > 0 &&
    publishPostsParams.value.tags.length <= 10 &&
    publishPostsParams.value.title.length > 0 &&
    publishPostsParams.value.content.length > 0
  );
});

// 清空发布参数并重置图片和分页
const clearPublishParams = () => {
  publishPostsParams.value = {
    title: '',
    content: '',
    category: '其他',
    tags: [],
    photos: [],
    status: 'approved',
  };
  paginated.value = {
    page: 0,
    pageSize: 10,
    totalCount: 0,
    totalPage: 1,
    sortField: 'createTime',
    sortOrder: 'asc',
  };
  userImages.value = [];
};

// 发布帖子的函数
const publishPosts = async () => {
  try {
    // 开始加载动画
    useLoading().start({
      title: '发布中',
      description: '帖子正在发布中，请稍候...',
    });
    // 调用 API 发布帖子
    await useApi().publishPost({
      ...publishPostsParams.value,
    });
    // 停止加载动画
    useLoading().stop();
    // 清空发布参数
    clearPublishParams();
    // 关闭对话框并传递成功标志
    close(true);
  } catch (err) {
    // 停止加载动画
    useLoading().stop();
  }
};

interface Image {
  id: string;
  url: string;
}

// 图片数据
const userImages = ref<Image[]>([]);
const loading = ref(false);

// 分页信息
const paginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

// 加载状态
const hasMore = computed(
  () => paginated.value.page < paginated.value.totalPage
);

// 获取用户上传的图片列表
const getUserImages = async (isLoadMore = false) => {
  if (!authStore.authStore?.uid) {
    return;
  }

  try {
    loading.value = true;

    const data = await useApi().getUserPhotosList(authStore.authStore?.uid, {
      page: paginated.value.page + 1,
      pageSize: 10, // 增加每页数量
      sortField: 'createTime',
      sortOrder: 'desc',
    });

    // 更新分页信息
    paginated.value = {
      page: data.page,
      pageSize: data.pageSize,
      totalCount: data.totalCount,
      totalPage: data.totalPage,
      sortField: data.sortField,
      sortOrder: data.sortOrder,
    };

    const newImages = data.list.map((item) => ({
      id: item.id,
      url: item.url,
    }));

    if (isLoadMore) {
      // 加载更多时，追加到现有数据
      userImages.value = [...userImages.value, ...newImages];
    } else {
      // 首次加载时，替换数据
      userImages.value = newImages;
    }
  } catch (err) {
    throw err;
  } finally {
    loading.value = false;
  }
};

// 加载更多图片
const loadMoreImages = async () => {
  if (loading.value || !hasMore.value) {
    return;
  }
  await getUserImages(true);
};

// 监听弹窗显示状态，重置数据
watch(
  () => show,
  (newShow) => {
    if (newShow) {
      clearPublishParams();
      getUserImages(false);
    }
  }
);

// 组件挂载时加载初始数据
if (show) {
  getUserImages(false);
}
</script>

<style lang="scss" scoped>
// 发布帖子的样式
.publish {
  > .form {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: start;

    > .form-item {
      width: 100%;
      display: flex;

      > div {
        margin-top: 0;
      }

      > :first-child {
        flex: 5;
      }

      > :last-child {
        flex: 3;
        margin-left: 1rem;
      }
    }

    .lab {
      margin-top: 1rem;
      display: flex;
      align-items: center;
      width: 100%;

      &:first-child {
        width: 15rem;
      }

      > span {
        margin-right: 2rem;
        font-size: 1.2rem;
      }

      > div {
        flex: 1;
      }
    }
  }

  > .posts-image-selector {
    margin-top: 1rem;
  }
}

// 对话框底部的样式
.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-cancel-hover);
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

