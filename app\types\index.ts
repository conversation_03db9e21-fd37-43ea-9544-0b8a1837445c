import type { MyFetchOptions, MyFetchRequest } from '~/plugins/request';

declare module '#app' {
  interface NuxtApp {
    /**
     * 封装的网络请求工具
     * @template T - 响应数据类型
     * @param {MyFetchRequest} url - 请求URL或配置对象
     * @param {MyFetchOptions} [options] - 可选请求配置
     * @returns {Promise<T>} 返回Promise，解析为响应数据
     */
    $request: <T = any>(
      url: MyFetchRequest,
      options?: MyFetchOptions
    ) => Promise<T>;

    /**
     * 全局消息提示工具
     * @param {setMessage} message - 消息配置对象
     */
    $message: (message: setMessage) => void;

    /**
     * 设备类型检测工具
     * @property {boolean} isMobile - 是否为移动设备
     * @property {boolean} isTablet - 是否为平板设备
     * @property {boolean} isDesktop - 是否为桌面设备
     * @property {DeviceType} type - 设备类型枚举
     */
    $device: {
      isMobile: boolean;
      isTablet: boolean;
      isDesktop: boolean;
      type: DeviceType;
    };

    /** 加载状态管理实例 */
    $loading: loadingState;
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    // 保持与NuxtApp相同的注释，确保一致性
    /** @inheritdoc */
    $request: <T = any>(
      url: MyFetchRequest,
      options?: MyFetchOptions
    ) => Promise<T>;

    /** @inheritdoc */
    $message: (message: setMessage) => void;

    /** @inheritdoc */
    $device: {
      isMobile: boolean;
      isTablet: boolean;
      isDesktop: boolean;
      type: DeviceType;
    };

    /** @inheritdoc */
    $loading: loadingState;
  }
}
