/**
 * 下载图片文件到用户本地设备
 * @param {File} imageFile - 要下载的图片文件对象
 *
 * 功能说明：
 * 1. 通过创建临时URL实现文件下载
 * 2. 自动处理文件名，当文件无名称时使用默认文件名
 * 3. 自动清理创建的资源，避免内存泄漏
 *
 * 实现原理：
 * 1. 使用URL.createObjectURL创建文件临时URL
 * 2. 创建隐藏的<a>标签触发下载
 * 3. 下载完成后自动移除临时资源
 *
 * 注意事项：
 * 1. 需要在浏览器环境中使用
 * 2. 文件大小受浏览器限制
 * 3. 跨域文件可能需要额外处理
 *
 * 使用示例：
 * const file = new File([blob], 'example.png');
 * downloadImage(file); // 下载名为example.png的图片
 */
export const downloadImage = (imageFile: File) => {
  // 创建临时URL：将File对象转换为可下载的URL
  const url = URL.createObjectURL(imageFile);
  // 创建隐藏的下载链接：动态生成<a>元素
  const a = document.createElement('a');
  // 设置下载链接的URL
  a.href = url;
  // 设置下载文件名，若无则使用默认文件名'cropped-image.png'
  a.download = imageFile.name || 'cropped-image.png';
  // 触发下载：将链接添加到DOM并模拟点击
  document.body.appendChild(a);
  a.click();
  // 清理资源：移除临时创建的DOM元素和URL
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

