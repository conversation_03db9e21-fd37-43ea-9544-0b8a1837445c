<template>
  <div class="default-layout">
    <AppHearder>
      <template #left>
        <!-- 返回按钮 -->
        <div class="back">
          <div @click="$router.push(routerStore.goBack() || '/')">
            <IconSvg
              name="leftArrows"
              size="1.5rem"
              color="var(--text-secondary)"
            />
          </div>
        </div>
      </template>
    </AppHearder>
    <main class="main">
      <div class="left">
        <AppSideMenu
          :navItems="navItems"
          :query="$route.query.key ? `?key=${$route.query.key}` : ''"
        />
      </div>
      <div class="right">
        <slot />
      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script lang="ts" setup>
import type { NavItem } from '~/components/AppSideMenu.vue';

const routerStore = useRouterStore();

// 导航条数据
const navItems: NavItem[] = [
  {
    name: '图片',
    path: '/search/images',
    icon: 'image',
  },
  {
    name: '帖子',
    path: '/search/posts',
    icon: 'posts',
  },
  {
    name: '用户',
    path: '/search/users',
    icon: 'mine',
  },
];
</script>

<style lang="scss" scoped>
/* 整个页面的容器样式 */
.default-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-base);
  color: var(--text-primary);

  .back {
    flex: 1;
    display: flex;

    > div {
      background-color: var(--background-elevated);
      padding: 0.7rem;
      border-radius: 1rem;
      cursor: pointer;
    }
  }

  /* 主要内容区域样式 */
  > .main {
    flex: 1;
    display: flex;
    margin-right: 0.5rem;
    min-height: 0; /* 修复flex容器滚动问题 */

    > .left {
      padding: 1rem 1rem;
    }

    > .right {
      flex: 1;
      background: var(--background-elevated);
      border-radius: 1rem;
      overflow: hidden;
      display: flex; /* 新增flex容器 */
      flex-direction: column; /* 垂直排列 */

      /* 滚动区域 */
      > * {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* 平滑滚动 */
      }
    }
  }
}
</style>
