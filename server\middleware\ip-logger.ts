import { getRequestIP } from 'h3';
import { getIpInfo } from '../utils/ip';
import { logger } from '../utils/logger';

// 定义需要忽略的路径
const IGNORED_PATHS = [
  '/favicon.ico',
  '/_nuxt/', // Nuxt静态资源
  '/_ipx/', // Nuxt图片优化
  '/__nuxt/', // Nuxt开发资源
  '/.well-known/', // 系统路径
  '/robots.txt',
  '/sitemap.xml',
  '/manifest.json',
  '/sw.js',
  '/workbox-',
  '/_debug/',
  '/_loading/',
  '/_error/',
];

// 检查路径是否应该被忽略
function shouldIgnorePath(path: string): boolean {
  return IGNORED_PATHS.some(
    (ignoredPath) => path.startsWith(ignoredPath) || path === ignoredPath
  );
}

export default defineEventHandler(async (event) => {
  const path = event.path;
  const method = event.method;

  // 开发环境跳过执行
  if (import.meta.dev) {
    return;
  }

  // 如果路径应该被忽略，直接返回
  if (shouldIgnorePath(path)) {
    return;
  }

  // 获取访问IP
  const ip = getRequestIP(event, { xForwardedFor: true });

  if (!ip) {
    logger.warn('IP-Logger', '无法获取IP地址', { path, method });
    return;
  }

  try {
    // 获取IP信息
    const ipInfo = await getIpInfo(ip);

    // 记录访问日志
    logger.info('Access', '页面访问', {
      ip,
      path,
      method,
      location: `${ipInfo.country}/${ipInfo.city}`,
      coords:
        ipInfo.latitude && ipInfo.longitude
          ? `${ipInfo.latitude},${ipInfo.longitude}`
          : undefined,
    });
  } catch (error) {
    // 如果IP解析失败，至少记录基本访问信息
    logger.info('Access', '页面访问', {
      ip,
      path,
      method,
      error:
        error instanceof Error
          ? { message: error.message, stack: error.stack }
          : String(error),
    });
  }
});
