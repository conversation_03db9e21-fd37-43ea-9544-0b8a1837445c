<template>
  <div class="page-container">
    <Head>
      <Title>{{ postDetail?.title || '帖子详情' }}</Title>
      <Meta
        name="keywords"
        :content="
          postDetail?.tags.join(',') +
          ',' +
          postDetail?.category +
          ',' +
          ',次元回廊,Dimensional Corridor,二次元,图片分享,交流平台,交友,壁纸'
        "
      />
    </Head>
    <MoblieNavBar>帖子详情</MoblieNavBar>

    <main class="main-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-container">
        <p>加载失败，请重试</p>
        <button @click="loadPostDetail" class="retry-btn">重试</button>
      </div>

      <!-- 帖子详情内容 -->
      <div v-else-if="postDetail" class="post-detail-content">
        <!-- 帖子主要内容区（标题+内容+图片） -->
        <div class="main-content-section">
          <!-- 帖子标题 -->
          <h1 class="post-title">{{ postDetail.title }}</h1>

          <!-- 帖子内容 -->
          <div class="post-content">{{ postDetail.content }}</div>

          <!-- 帖子图片区 -->
          <div
            class="images-container"
            v-if="postDetail.photos && postDetail.photos.length > 0"
          >
            <div class="images-grid">
              <div
                v-for="(photo, index) in postDetail.photos"
                :key="index"
                class="image-item"
                @click="navigateToPhoto(photo.id)"
              >
                <img
                  :src="photo.url + '?width=400'"
                  :alt="photo.filename"
                  loading="lazy"
                />
              </div>
            </div>
          </div>

          <!-- 帖子操作按钮 -->
          <div class="post-actions">
            <div
              class="action-button"
              @click="toggleFavorite"
              :disabled="actionLoading"
            >
              <IconSvg
                name="favorite"
                :size="20"
                :color="
                  postDetail.isFavorited
                    ? ICON_COLORS.favorite
                    : ICON_COLORS.inactive
                "
              />
            </div>
            <div
              class="action-button"
              @click="toggleLike"
              :disabled="actionLoading"
            >
              <IconSvg
                name="like"
                :size="20"
                :color="
                  postDetail.isLiked ? ICON_COLORS.like : ICON_COLORS.inactive
                "
              />
            </div>
          </div>
        </div>

        <!-- 用户信息区 -->
        <div class="user-section">
          <div class="user-info" @click="goToUserProfile">
            <img
              :src="postDetail.user.avatar"
              :alt="postDetail.user.nickname"
              class="user-avatar"
            />
            <div class="user-details">
              <h3 class="user-nickname">{{ postDetail.user.nickname }}</h3>
              <p class="user-id">ID: {{ postDetail.user.uid }}</p>
            </div>
          </div>
        </div>

        <!-- 帖子信息区 -->
        <div class="info-section">
          <div class="info-item">
            <IconSvg name="category" :size="16" :color="ICON_COLORS.inactive" />
            {{ postDetail.category }}
          </div>
          <div class="info-item" v-if="postDetail.photos?.length">
            <IconSvg name="image" :size="16" :color="ICON_COLORS.inactive" />
            {{ postDetail.photos.length }} 张图片
          </div>
        </div>

        <!-- 统计信息区 -->
        <div class="stats-section">
          <div class="stat-item">
            <IconSvg name="like" size="1.2rem" :color="ICON_COLORS.inactive" />
            <span>{{ postDetail.likeCount }}</span>
          </div>
          <div class="stat-item">
            <IconSvg
              name="favorite"
              size="1.2rem"
              :color="ICON_COLORS.inactive"
            />
            <span>{{ postDetail.favoriteCount }}</span>
          </div>
        </div>

        <!-- 标签区 -->
        <div
          class="tags-section"
          v-if="postDetail.tags && postDetail.tags.length > 0"
        >
          <h4>标签</h4>
          <div class="tags-container">
            <span
              v-for="(tag, index) in postDetail.tags"
              :key="index"
              class="tag"
              @click="searchByTag(tag)"
            >
              {{ tag }}
            </span>
          </div>
        </div>

        <!-- 相关推荐区 -->
        <div class="related-section" v-if="postDetail">
          <h4>相关推荐</h4>
          <div class="related-container">
            <RikkaPostsWaterfallGallery
              :posts="relatedPostsArray"
              :columns="0"
              :card-width="Number($device.isMobile ? 140 : 300)"
              :gap="10"
              :loading="relatedPostsLoading"
              :has-more="hasMoreRelatedPosts"
              @load-more="handleLoadMoreRelatedPosts"
              :height-limit="500"
            >
              <!-- 使用具名插槽自定义项目渲染 -->
              <template #item="{ items, columnWidth }">
                <MobilePostCard
                  v-for="post in items"
                  :key="post.id"
                  :post="post"
                  :fixed-width="columnWidth"
                />
              </template>
            </RikkaPostsWaterfallGallery>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script lang="ts" setup>
// 导入必要的组合式API和工具
const route = useRoute();
const router = useRouter();
const authStore = useAuthStore();
const { $device } = useNuxtApp();

// 获取帖子ID
const id = route.query.id as string;

// 响应式数据
const postDetail = ref<GetOtherPostsInfo | null>(null);
const loading = ref(true);
const error = ref(false);
const actionLoading = ref(false);

// 相关推荐数据
const relatedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const relatedPostsLoading = ref(false);
const relatedPostsPaginated = ref<Paginated>({
  page: 0,
  pageSize: 20,
  totalPage: 0,
  totalCount: 0,
  sortField: 'likeCount',
  sortOrder: 'desc',
});

// 统一的图标颜色常量
const ICON_COLORS = {
  like: '#FF5252',
  favorite: '#FFD700',
  inactive: 'var(--text-secondary)',
  white: '#FFFFFF',
};

// 计算属性
const hasMoreRelatedPosts = computed(() => {
  return (
    relatedPostsPaginated.value.page < relatedPostsPaginated.value.totalPage
  );
});

// 生命周期钩子
onMounted(() => {
  if (id) {
    loadPostDetail();
  } else {
    error.value = true;
    loading.value = false;
  }
});

// 监听路由变化
watch(
  () => route.query.id,
  (newId) => {
    if (newId && newId !== id) {
      window.location.reload(); // 重新加载页面以获取新的帖子详情
    }
  }
);

// 获取帖子详情
const loadPostDetail = async () => {
  if (!id) {
    error.value = true;
    loading.value = false;
    useMessage({
      name: '参数错误',
      description: '缺少帖子ID',
      type: 'error',
    });
    return;
  }

  loading.value = true;
  error.value = false;

  try {
    const data = await useApi().getOtherPostInfo(id);
    // 确保photos字段存在且是数组
    if (!data.photos) {
      data.photos = [];
    }
    // 确保tags字段存在且是数组
    if (!data.tags) {
      data.tags = [];
    }
    postDetail.value = data;
    // 加载相关推荐
    await loadRelatedPosts();
  } catch (err) {
    console.error('帖子详情', '获取帖子详情失败', err);
    error.value = true;
    useMessage({
      name: '加载失败',
      description: '无法获取帖子详情',
      type: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 切换点赞状态
const toggleLike = async () => {
  if (!postDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '点赞功能需要登录后使用',
      type: 'info',
    });
    router.push('/mobile/auth/login');
    return;
  }

  actionLoading.value = true;
  try {
    await useApi().toggleLikePost(postDetail.value.id);
    // 更新本地状态
    postDetail.value.isLiked = !postDetail.value.isLiked;
    postDetail.value.likeCount += postDetail.value.isLiked ? 1 : -1;

    useMessage({
      name: postDetail.value.isLiked ? '点赞成功' : '取消点赞',
      description: '',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  } finally {
    actionLoading.value = false;
  }
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!postDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '收藏功能需要登录后使用',
      type: 'info',
    });
    router.push('/mobile/auth/login');
    return;
  }

  actionLoading.value = true;
  try {
    await useApi().toggleFavoritePost(postDetail.value.id);
    // 更新本地状态
    postDetail.value.isFavorited = !postDetail.value.isFavorited;
    postDetail.value.favoriteCount += postDetail.value.isFavorited ? 1 : -1;

    useMessage({
      name: postDetail.value.isFavorited ? '收藏成功' : '取消收藏',
      description: '',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  } finally {
    actionLoading.value = false;
  }
};

// 跳转到用户主页
const goToUserProfile = () => {
  if (postDetail.value?.user.uid) {
    router.push(`/mobile/user/${postDetail.value.user.uid}`);
  }
};

// 导航到图片详情
const navigateToPhoto = (photoId: string) => {
  router.push({
    path: '/mobile/photos/details',
    query: { id: photoId },
  });
};

// 根据标签搜索
const searchByTag = (tag: string) => {
  router.push({
    path: '/mobile/search',
    query: { q: tag, type: 'posts' },
  });
};

/** 获取相关推荐帖子列表 */
const getRelatedPostsList = async (query?: PostsListQuery) => {
  if (!postDetail.value) return;

  try {
    const { list, ...res } = await useApi().getPostList({
      sortField: 'likeCount',
      sortOrder: 'desc',
      tags: ['', '', ...(postDetail.value.tags || [])],
      ...query,
    });

    // 处理每条帖子的关联图片信息并过滤掉当前帖子
    const newData = await Promise.all(
      list
        .filter((item) => item.id !== postDetail.value?.id)
        .map(async (v) => {
          const { photos, ...rest } = v;
          if (!photos || photos.length === 0 || !photos[0]) return v;

          return {
            ...rest,
            photos, // 保留原有 photos 字段
            cover: {
              url: photos[0].url,
              filename: photos[0].filename,
              width: photos[0].attributes.width,
              height: photos[0].attributes.height,
            },
          };
        })
    );

    relatedPostsArray.value.push(...newData);
    relatedPostsPaginated.value = res;
  } catch (err) {
    console.error('相关推荐', '获取相关推荐失败', err);
    throw err;
  }
};

/** 初始化相关推荐数据 */
const loadRelatedPosts = async () => {
  if (!postDetail.value) return;

  relatedPostsLoading.value = true;
  try {
    // 清空原有数据，重新加载
    relatedPostsArray.value = [];
    await getRelatedPostsList({ page: 1, pageSize: 20 });
  } catch (err) {
    console.error('初始化相关推荐失败:', err);
  } finally {
    relatedPostsLoading.value = false;
  }
};

/** 加载更多相关推荐 */
const handleLoadMoreRelatedPosts = async () => {
  if (relatedPostsLoading.value || !hasMoreRelatedPosts.value) {
    return;
  }
  relatedPostsLoading.value = true;
  try {
    await getRelatedPostsList({
      page: relatedPostsPaginated.value.page + 1,
      pageSize: 20,
    });
  } catch (err) {
    console.error('加载更多相关推荐失败:', err);
    useMessage({
      name: '加载失败',
      description: '无法加载更多推荐内容',
      type: 'error',
    });
  } finally {
    relatedPostsLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: auto;
  background-color: var(--background-base);
  color: var(--text-primary);
}

.main-content {
  flex: 1;
  overflow: auto;
  background: var(--background-elevated);
  padding: 0;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 20rem;
  gap: 1rem;

  .loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 0.2rem solid var(--background-secondary);
    border-top: 0.2rem solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 20rem;
  gap: 1rem;

  p {
    color: var(--text-secondary);
    font-size: 0.9rem;
  }

  .retry-btn {
    padding: 0.5rem 1rem;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
  }
}

// 帖子详情内容
.post-detail-content {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  background: linear-gradient(
    180deg,
    var(--background-elevated) 0%,
    var(--background-base) 100%
  );
}

// 主要内容区（标题+内容+图片合并）
.main-content-section {
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1.2rem;
  padding: 0.5rem;
  box-shadow:
    0 0.5rem 2rem rgba(0, 0, 0, 0.15),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.1rem;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--interactive-primary) 50%,
      transparent 100%
    );
    border-radius: 1.2rem 1.2rem 0 0;
  }

  .post-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 1.5rem 0;
    line-height: 1.4;
    text-align: center;
    position: relative;
  }

  .post-content {
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-secondary);
    white-space: pre-wrap;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 0.75rem;
    border: 0.05rem solid rgba(255, 255, 255, 0.1);
  }

  .images-container {
    margin-bottom: 1.5rem;

    .images-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 0.75rem;

      .image-item {
        aspect-ratio: 1;
        overflow: hidden;
        border-radius: 0.75rem;
        cursor: pointer;
        box-shadow:
          0 0.25rem 1rem rgba(0, 0, 0, 0.15),
          0 0 0 0.05rem rgba(255, 255, 255, 0.1);
        transition: transform 0.2s ease;

        &:active {
          transform: scale(0.95);
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  // 帖子操作按钮
  .post-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding: 0.5rem 1.5rem;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(15px);
    border-radius: 2rem;
    box-shadow:
      0 0.5rem 2rem rgba(0, 0, 0, 0.4),
      0 0 0 0.1rem rgba(255, 255, 255, 0.1),
      var(--shadow-neon-primary);
    border: 0.05rem solid rgba(255, 255, 255, 0.1);

    .action-button {
      width: 2.75rem;
      height: 2.75rem;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.15);
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border: 0.1rem solid rgba(255, 255, 255, 0.2);

      &:active {
        transform: scale(0.95);
      }
    }
  }
}

// 用户信息区（参考图片详情页设计）
.user-section {
  .user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 1rem;
    cursor: pointer;
    box-shadow:
      0 0.25rem 1rem rgba(0, 0, 0, 0.1),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05);
    border-left: 0.25rem solid var(--interactive-primary);

    .user-avatar {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: 50%;
      object-fit: cover;
      border: 0.125rem solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.2);
    }

    .user-details {
      flex: 1;

      .user-nickname {
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem 0;
        color: var(--text-primary);
      }

      .user-id {
        font-size: 0.8rem;
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }
}

// 帖子信息区
.info-section {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;

  .info-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 0.75rem;
    font-size: 0.85rem;
    color: var(--text-secondary);
    box-shadow:
      0 0.125rem 0.5rem rgba(0, 0, 0, 0.08),
      0 0 0 0.05rem rgba(255, 255, 255, 0.05);
    border: 0.05rem solid rgba(255, 255, 255, 0.1);
  }
}

// 统计信息区
.stats-section {
  display: flex;
  justify-content: center;
  padding: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1rem;
  gap: 2rem;
  box-shadow:
    0 0.25rem 1rem rgba(0, 0, 0, 0.1),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.05rem;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--interactive-primary) 50%,
      transparent 100%
    );
    border-radius: 1rem 1rem 0 0;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 0.5rem;

    span {
      font-weight: 600;
    }
  }
}

// 标签区
.tags-section {
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1rem;
  padding: 1.25rem;
  box-shadow:
    0 0.25rem 1rem rgba(0, 0, 0, 0.1),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);

  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
    padding-left: 1rem;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 0.25rem;
      height: 1rem;
      background: var(--interactive-primary);
      border-radius: 0.125rem;
    }
  }

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;

    .tag {
      padding: 0.5rem 1rem;
      background: linear-gradient(
        135deg,
        var(--background-floating) 0%,
        var(--background-surface) 100%
      );
      border-radius: 1.5rem;
      font-size: 0.8rem;
      color: var(--text-secondary);
      cursor: pointer;
      border: 0.05rem solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    }
  }
}

// 相关推荐区
.related-section {
  background: linear-gradient(
    135deg,
    var(--background-elevated) 0%,
    var(--background-floating) 100%
  );
  border-radius: 1.2rem;
  padding: 1.5rem;
  box-shadow:
    0 0.5rem 2rem rgba(0, 0, 0, 0.15),
    0 0 0 0.1rem rgba(255, 255, 255, 0.05);
  border-left: 0.25rem solid var(--interactive-primary);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0.1rem;
    background: linear-gradient(
      90deg,
      transparent 0%,
      var(--interactive-primary) 50%,
      transparent 100%
    );
    border-radius: 1.2rem 1.2rem 0 0;
  }

  h4 {
    margin: 0 0 1.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    position: relative;
    padding-left: 1.5rem;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 0.3rem;
      height: 1.2rem;
      background: linear-gradient(
        180deg,
        var(--interactive-primary) 0%,
        var(--interactive-primary-hover) 100%
      );
      border-radius: 0.15rem;
      box-shadow: 0 0 0.5rem var(--interactive-primary-translucent);
    }

    &::after {
      content: '';
      position: absolute;
      left: 0.5rem;
      top: 50%;
      transform: translateY(-50%);
      width: 0.1rem;
      height: 0.8rem;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 0.05rem;
    }
  }

  .related-container {
    // 瀑布流组件会自己处理样式
    width: 100%;

    :deep(.waterfall-container) {
      padding: 0;
    }
  }
}
</style>

