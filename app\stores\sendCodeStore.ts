export const useSendCodeStore = defineStore(
  'sendCode',
  () => {
    const email = ref(new Date('2000-1-1'));
    const phone = ref(new Date('2000-1-1'));
    const setEmailDate = () => {
      email.value = new Date();
    };
    const setPhoneDate = () => {
      phone.value = new Date();
    };
    // 获取验证码发送时间
    const getCodeDate = () => {
      return {
        email: email.value,
        phone: phone.value,
      };
    };

    return {
      email,
      phone,
      setEmailDate,
      setPhoneDate,
      getCodeDate,
    };
  },
  {
    // nuxt项目中将cookie作为持久化存储，刷新页面后依然存在
    persist: {
      storage: piniaPluginPersistedstate.cookies({
        maxAge: 60 * 2, // 2分钟
      }),
    },
  }
);

