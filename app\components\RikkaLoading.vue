<template>
  <div v-if="displayVisible" class="loading-overlay">
    <div class="magic-container">
      <!-- 魔法阵背景 -->
      <div class="magic-pattern"></div>

      <!-- 多层魔法阵 -->
      <div class="magic-circle outer-circle"></div>
      <div class="magic-circle middle-circle"></div>
      <div class="magic-circle inner-circle"></div>

      <!-- 复杂魔法阵 (替代眼睛) -->
      <div class="complex-rune">
        <div class="rune-circle rune-outer"></div>
        <div class="rune-circle rune-middle"></div>
        <div class="rune-inner">
          <div
            class="rune-segment"
            v-for="(segment, index) in 6"
            :key="index"
          ></div>
          <div class="rune-core"></div>
        </div>
      </div>

      <!-- 进度指示器 -->
      <div class="progress-ring">
        <svg viewBox="0 0 100 100" class="progress-svg">
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stop-color="var(--progress-gradient-start)" />
              <stop offset="100%" stop-color="var(--progress-gradient-end)" />
            </linearGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3.5" result="coloredBlur" />
              <feMerge>
                <feMergeNode in="coloredBlur" />
                <feMergeNode in="SourceGraphic" />
              </feMerge>
            </filter>
          </defs>
          <circle cx="50" cy="50" r="45" class="progress-background"></circle>
          <circle
            cx="50"
            cy="50"
            r="45"
            class="progress-circle"
            :stroke-dasharray="circumference"
            :stroke-dashoffset="strokeDashoffset"
          ></circle>
        </svg>
        <div class="progress-text">{{ formattedProgress }}%</div>
      </div>

      <!-- 文本区域 -->
      <div class="text-container">
        <h2 class="title">{{ title }}</h2>
        <p class="description">{{ description }}</p>
      </div>

      <!-- 魔法粒子效果 -->
      <div class="particles">
        <div
          v-for="(particle, index) in particles"
          :key="index"
          :style="particleStyle(particle)"
          class="particle"
        ></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { CSSProperties } from 'vue';

// 定义粒子类型
interface Particle {
  x: number; // 粒子x坐标
  y: number; // 粒子y坐标
  size: number; // 粒子大小
  tx: number; // 目标x位置
  ty: number; // 目标y位置
  opacity: number; // 透明度
  duration: number; // 动画持续时间
}

// 从Nuxt全局注入获取loading状态
const { $loading } = useNuxtApp();
// 将全局loading状态转换为ref
const { visible, title, description } = toRefs($loading);

// 响应式状态
const displayVisible = ref(false); // 控制组件显示/隐藏
const progress = ref(0); // 进度值(0-100)
const particles = ref<Particle[]>([]); // 粒子数组
const interval = ref<NodeJS.Timeout | null>(null); // 粒子动画计时器
const progressInterval = ref<NodeJS.Timeout | null>(null); // 进度动画计时器

// 圆形进度条周长
const circumference = 2 * Math.PI * 45;

// 计算进度条偏移量
const strokeDashoffset = computed(() => {
  return circumference * (1 - progress.value / 100);
});

// 格式化进度显示(整数百分比)
const formattedProgress = computed(() => {
  return progress.value.toFixed(0);
});

// 生成粒子样式对象
const particleStyle = (particle: Particle): CSSProperties => {
  return {
    left: `${particle.x}px`,
    top: `${particle.y}px`,
    width: `${particle.size}px`,
    height: `${particle.size}px`,
    opacity: particle.opacity,
    background: `radial-gradient(circle, var(--particle-color-start), var(--particle-color-end))`,
    filter: 'blur(0.5px)',
  };
};

// 创建随机粒子
const createParticles = () => {
  particles.value = Array.from({ length: 30 }, () => ({
    x: Math.random() * 340,
    y: Math.random() * 420,
    size: Math.random() * 5 + 3,
    tx: (Math.random() - 0.5) * 350,
    ty: (Math.random() - 0.5) * 350,
    opacity: Math.random() * 0.6 + 0.4,
    duration: Math.random() * 5 + 4,
  }));
};

// 更新粒子位置和状态
const animateParticles = () => {
  particles.value = particles.value.map((p: Particle) => {
    if (p.opacity <= 0) {
      // 重置消失的粒子
      return {
        x: Math.random() * 340,
        y: Math.random() * 420,
        size: Math.random() * 5 + 3,
        tx: (Math.random() - 0.5) * 350,
        ty: (Math.random() - 0.5) * 350,
        opacity: Math.random() * 0.6 + 0.4,
        duration: Math.random() * 5 + 4,
      };
    } else {
      // 更新粒子位置和透明度
      const speed = 0.5;
      return {
        ...p,
        x: p.x + (p.tx / 100) * speed,
        y: p.y + (p.ty / 100) * speed,
        opacity: Math.max(0, p.opacity - 1 / (p.duration * 20)),
      };
    }
  });
};

// 监听全局visible状态变化
watch(
  visible,
  async (newVal: boolean) => {
    if (newVal) {
      // 显示加载状态
      displayVisible.value = true;
      progress.value = 0;
      createParticles();

      // 启动粒子动画
      if (interval.value) clearInterval(interval.value);
      interval.value = setInterval(animateParticles, 50);

      // 启动进度动画
      if (progressInterval.value) clearInterval(progressInterval.value);
      const fastIncrementPerUpdate = 90 / (1500 / 50); // 快速阶段增量
      const slowIncrementPerUpdate = 0.1; // 慢速阶段增量

      progressInterval.value = setInterval(() => {
        if (progress.value < 90) {
          progress.value += fastIncrementPerUpdate;
          if (progress.value >= 90) progress.value = 90;
        } else if (progress.value < 99) {
          progress.value += slowIncrementPerUpdate;
          if (progress.value >= 99) progress.value = 99;
        }
      }, 50);
    } else {
      // 隐藏加载状态
      progress.value = 100;
      await nextTick();

      // 清除动画
      if (interval.value) clearInterval(interval.value);
      if (progressInterval.value) clearInterval(progressInterval.value);

      // 延迟隐藏组件
      setTimeout(() => {
        particles.value = [];
        displayVisible.value = false;
      }, 1000);
    }
  },
  { immediate: true }
);

// 组件卸载前清理
onBeforeUnmount(() => {
  if (interval.value) clearInterval(interval.value);
  if (progressInterval.value) clearInterval(progressInterval.value);
});
</script>

<style scoped lang="scss">
.loading-overlay {
  // 小鸟游六花风格配色 (紫色/蓝色调)
  --loading-bg: rgba(26, 5, 46, 0.98); // 更深的紫蓝色背景
  --magic-circle-outer-color: #8a2be2; // 蓝紫色
  --magic-circle-middle-color: #a020f0; // 紫色
  --magic-circle-inner-color: #ba55d3; // 中等兰花紫

  // 进度条
  --progress-gradient-start: #9932cc; // 深兰花紫
  --progress-gradient-end: #da70d6; // 兰花紫
  --progress-text-color: #ffd700; // 神光金色

  // 粒子
  --particle-color-start: #8a2be2;
  --particle-color-end: #ee82ee; // 紫罗兰

  // 文本
  --title-color: #e0d5ff; // 浅紫色
  --description-color: rgba(224, 213, 255, 0.8); // 半透明浅紫

  // 魔法阵符文
  --rune-color: #9370db; // 中等紫色
  --rune-core-color: #ffd700; // 神光金色

  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--loading-bg);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(8px); // 增强背景模糊
  font-family: 'Noto Sans SC', 'Arial', sans-serif;

  .magic-container {
    position: relative;
    width: 360px; // 略微增大容器
    height: 450px; // 略微增大容器
    display: flex;
    justify-content: center;
    align-items: center;
    perspective: 1000px; // 添加透视效果

    .magic-pattern {
      position: absolute;
      width: 320px; // 调整大小
      height: 320px;
      background-image:
        radial-gradient(
          circle at center,
          transparent 40%,
          rgba(138, 43, 226, 0.15) 100% // 调整颜色
        ),
        repeating-conic-gradient(
          from 0deg,
          rgba(138, 43, 226, 0.25) 0deg 10deg,
          // 调整颜色
          transparent 10deg 20deg
        );
      border-radius: 50%;
      animation: rotate 30s linear infinite;
      opacity: 0.9; // 略微提高不透明度
      box-shadow: 0 0 30px rgba(138, 43, 226, 0.4); // 增加阴影
    }

    .magic-circle {
      position: absolute;
      border-radius: 50%;
      border-style: solid;
      animation: rotate linear infinite;
      box-shadow:
        0 0 25px currentColor,
        0 0 50px rgba(255, 215, 0, 0.1); // 添加金色微光

      &.outer-circle {
        width: 320px; // 调整大小
        height: 320px;
        border-width: 3px; // 略微加粗
        border-color: var(--magic-circle-outer-color);
        animation-duration: 25s;
        color: rgba(138, 43, 226, 0.5); // 匹配新颜色
      }

      &.middle-circle {
        width: 260px; // 调整大小
        height: 260px;
        border-width: 2px; // 略微加粗
        border-color: var(--magic-circle-middle-color);
        animation-duration: 20s;
        animation-direction: reverse;
        color: rgba(160, 32, 240, 0.4); // 匹配新颜色
      }

      &.inner-circle {
        width: 200px; // 调整大小
        height: 200px;
        border-width: 1.5px; // 略微加粗
        border-color: var(--magic-circle-inner-color);
        animation-duration: 15s;
        color: rgba(186, 85, 211, 0.3); // 匹配新颜色
      }
    }

    /* 复杂魔法阵样式 */
    .complex-rune {
      position: absolute;
      width: 130px; // 略微增大
      height: 130px;
      z-index: 10; // 保持魔法阵的 z-index
      animation:
        float 4s ease-in-out infinite,
        rotate 18s linear infinite reverse; // 添加旋转动画
      filter: drop-shadow(0 0 15px var(--rune-color)); // 添加整体发光

      .rune-circle {
        position: absolute;
        border-radius: 50%;
        border: 2px solid var(--rune-color); // 略微加粗
        box-shadow:
          0 0 15px var(--rune-color),
          0 0 30px rgba(255, 215, 0, 0.1); // 增加阴影和金色微光
        opacity: 0.9;

        &.rune-outer {
          width: 100%;
          height: 100%;
          border-width: 3px; // 加粗
          animation: rotate 15s linear infinite;
          box-shadow:
            0 0 20px var(--rune-color),
            0 0 40px rgba(255, 215, 0, 0.2); // 增强阴影和金色微光

          &::before,
          &::after {
            content: '';
            position: absolute;
            width: 12px; // 略微增大
            height: 12px; // 略微增大
            background: var(--rune-color);
            border-radius: 50%;
            top: 50%;
            transform: translateY(-50%);
            box-shadow: 0 0 15px var(--rune-color); // 增加阴影
            filter: drop-shadow(0 0 8px var(--rune-color));
          }
        }

        &.rune-middle {
          width: 90px; // 略微增大
          height: 90px;
          top: 20px;
          left: 20px;
          border-width: 1.5px; // 略微加粗
          animation: rotate 12s linear infinite reverse;
          box-shadow:
            0 0 12px var(--rune-color),
            0 0 25px rgba(255, 215, 0, 0.15); // 增加阴影和金色微光

          &::before,
          &::after {
            content: '';
            position: absolute;
            width: 10px; // 略微增大
            height: 10px; // 略微增大
            background: var(--rune-color);
            border-radius: 50%;
            left: 50%;
            transform: translateX(-50%);
            box-shadow: 0 0 12px var(--rune-color); // 增加阴影
            filter: drop-shadow(0 0 6px var(--rune-color));
          }
        }
      }

      .rune-inner {
        position: absolute;
        width: 60px; // 略微增大
        height: 60px;
        top: 35px;
        left: 35px;
        animation: rotate 10s linear infinite;

        .rune-segment {
          position: absolute;
          width: 5px; // 略微加粗
          height: 30px; // 略微增长
          background: var(--rune-color);
          left: 50%;
          top: 0;
          transform-origin: bottom center;
          margin-left: -2.5px; // 居中调整
          box-shadow: 0 0 8px var(--rune-color); // 增加阴影
          filter: drop-shadow(0 0 5px var(--rune-color));

          @for $i from 1 through 6 {
            &:nth-child(#{$i}) {
              transform: rotate(#{($i - 1) * 60}deg); // 保持旋转角度
            }
          }
        }

        .rune-core {
          position: absolute;
          width: 25px; // 略微增大
          height: 25px;
          background: var(--rune-core-color); // 神光金色
          border-radius: 50%;
          top: 17.5px; // 居中调整
          left: 17.5px; // 居中调整
          box-shadow:
            0 0 20px var(--rune-core-color),
            0 0 40px var(--rune-core-color),
            inset 0 0 15px rgba(0, 0, 0, 0.6); // 增强核心发光和内阴影
          animation: pulse 1.8s infinite alternate; // 调整脉冲动画速度
          filter: blur(0.5px) drop-shadow(0 0 10px var(--rune-core-color)); // 添加模糊和发光
        }
      }
    }

    .progress-ring {
      position: absolute;
      width: 280px; // 略微增大
      height: 280px;
      filter: drop-shadow(0 0 10px var(--progress-text-color)); // 添加整体发光
      z-index: 20; // 确保进度环及其文本在魔法阵之上

      .progress-svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
      }

      .progress-background {
        fill: none;
        stroke: rgba(153, 50, 204, 0.2); // 调整背景色
        stroke-width: 5; // 略微加粗
      }

      .progress-circle {
        fill: none;
        stroke: url(#gradient);
        stroke-width: 5; // 略微加粗
        stroke-linecap: round;
        transition: stroke-dashoffset 0.5s cubic-bezier(0.33, 1, 0.68, 1);
        filter: url(#glow); // 应用发光滤镜
      }

      .progress-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 30px; // 增大字体
        font-weight: bold;
        color: var(--progress-text-color); // 神光金色
        text-shadow:
          0 0 15px var(--progress-text-color),
          0 0 25px rgba(255, 215, 0, 0.5); // 增强金色发光
        font-family: 'Courier New', monospace;
        z-index: 1; // 在 progress-ring 内部，保持相对层级，因为父级 z-index 已经很高
      }
    }

    .text-container {
      position: absolute;
      bottom: 10px; // 调整位置
      text-align: center;
      width: 100%;
      padding: 30px 20px;
      z-index: 25; // 确保标题和描述在最上层

      .title {
        font-size: 28px; // 略微增大
        margin-bottom: 10px;
        color: var(--title-color);
        letter-spacing: 2px; // 增加字间距
        font-weight: 700;
        text-shadow:
          0 0 12px rgba(138, 43, 226, 0.9),
          0 0 20px rgba(255, 215, 0, 0.2); // 增强阴影并添加金色微光
      }

      .description {
        font-size: 19px; // 略微增大
        color: var(--description-color);
        max-width: 320px; // 略微增大宽度
        margin: 0 auto;
        line-height: 1.6; // 调整行高
      }
    }

    .particles {
      position: absolute;
      width: 100%;
      height: 100%;
      pointer-events: none;
      filter: blur(0.5px); // 对粒子容器也添加模糊

      .particle {
        position: absolute;
        border-radius: 50%;
        animation: particle-fade 5s infinite ease-out; // 添加粒子淡入淡出动画
      }
    }
  }
}

/* 定义动画 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(10deg); // 增加浮动和旋转
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1.15); // 增大脉冲幅度
    opacity: 1;
  }
}

@keyframes particle-fade {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  20% {
    opacity: 1;
    transform: scale(1);
  }
  80% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: scale(1.2);
  }
}
</style>

