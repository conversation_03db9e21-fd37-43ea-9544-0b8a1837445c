<template>
  <transition name="modal">
    <!-- 使用transition标签实现模态框的动画效果 -->
    <div v-if="show" class="modal-mask" @click.self="maskClosable">
      <!-- 新增遮罩层点击关闭功能；当show为true时显示模态框遮罩层 -->
      <div class="modal-wrapper">
        <!-- 包裹整个模态框内容的容器 -->
        <div class="modal-container" :style="{ width }">
          <!-- 模态框的主要容器，宽度可以通过width属性控制 -->
          <!-- 头部 -->
          <div class="modal-header" v-if="showHeader">
            <!-- 当showHeader为true时显示模态框头部 -->
            <slot name="header">
              <!-- 如果用户没有提供header插槽内容，则显示默认的标题 -->
              <h3>{{ title }}</h3>
              <span v-if="showClose" class="close-btn" @click="close()">×</span>
              <!-- 当showClose为true时显示关闭按钮，点击按钮会触发close方法 -->
            </slot>
          </div>

          <!-- 内容 -->
          <div class="modal-body" :style="{ maxHeight: maxDodyHeight }">
            <!-- 模态框的内容区域，最大高度可以通过maxDodyHeight属性控制 -->
            <slot></slot>
            <!-- 默认的插槽内容，用于插入模态框的主要内容 -->
          </div>

          <!-- 底部 -->
          <div v-if="showFooter" class="modal-footer">
            <!-- 当showFooter为true时显示模态框底部 -->
            <slot name="footer"> </slot>
            <!-- 如果用户没有提供footer插槽内容，则底部为空 -->
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script lang="ts" setup>
// 定义组件的属性
const props = defineProps({
  show: Boolean,
  title: {
    type: String,
    default: '提示',
  },
  width: {
    type: String,
    default: 'auto',
  },
  maxDodyHeight: {
    type: String,
    default: '60vh',
  },
  maskClosable: {
    type: Boolean,
    default: true,
  },
  showHeader: {
    type: Boolean,
    default: true,
  },
  showClose: {
    type: Boolean,
    default: true,
  },
  showFooter: {
    type: Boolean,
    default: true,
  },
});

// 定义组件触发的事件
const emit = defineEmits<{
  close: [value: boolean];
}>();

// 关闭模态框的方法，可选参数flag默认为false
const close = (flag = false) => {
  emit('close', flag);
};

// 新增点击遮罩层关闭弹窗的功能；检查maskClosable属性是否为true，如果是则调用close方法
const maskClosable = () => {
  if (props.maskClosable) {
    close();
  }
};
</script>

<style lang="scss" scoped>
// 整个模态框的遮罩层样式
.modal-mask {
  position: fixed;
  z-index: 99;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--background-overlay);
  display: grid;
  place-items: center;
  transition: opacity 0.3s;

  .modal-wrapper {
    max-width: 80vw;
    max-height: 80vh;

    // 移动端优化 - 允许更大的宽度
    @media (max-width: 768px) {
      max-width: 98vw;
      max-height: 90vh;
      margin: 1rem;
    }
  }

  .modal-container {
    background: var(--background-floating);
    border-radius: 1rem;
    box-shadow: var(--shadow-neon-primary);
    padding: 1.5rem;
    transition: all 0.3s;

    // 移动端优化 - 减少内边距
    @media (max-width: 768px) {
      padding: 1rem;
      border-radius: 0.5rem;
    }

    @media (max-width: 480px) {
      padding: 0.75rem;
    }
  }
}

// 模态框头部样式
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 1rem;
  border-bottom: var(--border-focus-ring);

  h3 {
    color: var(--text-primary);
    font-size: 1.4rem;
  }

  .close-btn {
    cursor: pointer;
    font-size: 2rem;
    color: var(--text-primary);
    transition: color 0.2s;

    &:hover {
      color: var(--text-secondary);
    }
  }
}

// 模态框内容区域样式
.modal-body {
  padding: 1rem;
  max-height: 60vh;
  overflow-y: auto;
  color: var(--text-primary);
}

// 模态框底部样式
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 1rem;
  border-top: var(--border-focus-ring);
}

/* 动画 */
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}
</style>

