<template>
  <header class="header">
    <div class="left">
      <slot name="left">
        <!-- 返回按钮 -->
        <div class="back">
          <div @click="$router.push(routerStore.goBack() || '/')">
            <IconSvg
              name="leftArrows"
              size="2rem"
              color="var(--text-secondary)"
            />
          </div>
        </div>
      </slot>
    </div>
    <div class="center">
      <slot>
        <VanishingInput
          v-if="isSearch"
          v-model="text"
          :placeholders="placeholders"
          @submit="handleSearchSubmit"
          :show-button="!text"
        >
          <template #submit-icon>
            <div>
              <IconSvg
                name="search"
                size="2rem"
                color="var(--text-secondary)"
              />
            </div>
          </template>
        </VanishingInput>
      </slot>
    </div>
    <div class="right">
      <slot name="right">
        <div class="search-btn" @click="handleSearchSubmit" v-if="isSearch">
          搜索
        </div>

        <div class="search" v-else>
          <div @click="$router.push('/mobile/search')">
            <IconSvg name="search" size="2rem" color="var(--text-secondary)" />
          </div>
        </div>
      </slot>
    </div>
  </header>
</template>

<script lang="ts" setup>
// 引入路由状态管理
const routerStore = useRouterStore();
const router = useRouter();

const props = defineProps({
  isSearch: {
    type: Boolean,
    default: false,
  },
});

// 搜索框占位符
const placeholders = ref(['六花']);
// 搜索框文本
const text = ref('');

// 请求获取搜索框占位符
const getSearchPlaceholders = async () => {
  try {
    placeholders.value = await useApi().getSearchHotwords();
    // 如果获取的占位符为空，则使用默认占位符
    if (placeholders.value.length === 0) {
      placeholders.value = ['六花'];
    }
  } catch (err) {}
};
getSearchPlaceholders();

// 搜索框提交事件处理函数
const handleSearchSubmit = () => {
  if (!routerStore.get()?.includes('search')) {
    router.push({ path: '/mobile/search', query: { key: text.value } });
    return;
  }

  router.push({ path: routerStore.get(), query: { key: text.value } }); // 更换搜索字符串
};
</script>

<style lang="scss" scoped>
/* 头部样式 */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  height: 5rem;

  > .left {
    flex: 1;
    display: flex;

    > div {
      background-color: var(--background-elevated);
      padding: 0.7rem;
      border-radius: 1rem;
      cursor: pointer;
    }
  }

  > .center {
    font-size: 1.5rem;
  }

  > .right {
    flex: 1;
    display: flex;
    flex-direction: row-reverse;

    > .search {
      background-color: var(--background-elevated);
      padding: 0.7rem;
      border-radius: 1rem;
      cursor: pointer;
    }

    .search-btn {
      font-size: 1.6rem;
      cursor: pointer;
    }
  }
}
</style>

