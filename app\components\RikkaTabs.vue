<template>
  <div class="tabs-container">
    <!-- 选项卡标题区域 -->
    <div
      class="tabs-header"
      role="tablist"
      ref="tabsHeaderRef"
      tabindex="0"
      @keydown="onTabsHeaderKeydown"
    >
      <button
        v-for="(tab, index) in tabs"
        :key="index"
        @click="setActiveTab(index)"
        :class="['tab-button', { active: activeIndex === index }]"
        :aria-selected="activeIndex === index"
        role="tab"
        :tabindex="activeIndex === index ? 0 : -1"
        ref="tabButtons"
      >
        {{ tab.title }}
      </button>
      <div class="tab-slider" :style="sliderStyle" aria-hidden="true"></div>
    </div>

    <!-- 选项卡内容区域 -->
    <div class="tabs-content">
      <Transition :name="transitionName" mode="out-in">
        <div class="tab-panels" :key="activeIndex">
          <div
            v-for="(tab, index) in tabs"
            v-show="activeIndex === index"
            :key="index"
            class="tab-panel"
            role="tabpanel"
          >
            <slot :name="tab.slotName"></slot>
          </div>
        </div>
      </Transition>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  tabs: {
    type: Array as () => Array<{ title: string; slotName: string }>,
    required: true,
    validator: (value: any) => {
      return value.every(
        (tab: any) =>
          typeof tab.title === 'string' && typeof tab.slotName === 'string'
      );
    },
  },
  initialTab: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['activeTab']);

const activeIndex = ref(props.initialTab);
const lastIndex = ref(props.initialTab);
const tabButtons = ref([]);
const tabsHeaderRef = ref<HTMLElement | null>(null);
const sliderStyle = ref({ left: '0px', width: '0px' });

const SLIDER_INSET_REM = 1.2; // rem

function getRemPx() {
  if (typeof window !== 'undefined') {
    return parseFloat(getComputedStyle(document.documentElement).fontSize);
  }
  return 16;
}

function setActiveTab(index: number) {
  if (index >= 0 && index < props.tabs.length) {
    activeIndex.value = index;
    emit('activeTab', index);
  }
}

function updateSlider() {
  nextTick(() => {
    const btns = tabButtons.value as HTMLElement[];
    const header = tabsHeaderRef.value;
    if (btns && btns[activeIndex.value] && header) {
      const btn = btns[activeIndex.value] as HTMLElement;
      const headerRect = header.getBoundingClientRect();
      const btnRect = btn.getBoundingClientRect();
      const rem = getRemPx();
      const insetPx = SLIDER_INSET_REM * rem;
      let width = btnRect.width - insetPx * 2;
      if (width < 8) width = 8; // 最小宽度
      const left = btnRect.left - headerRect.left + header.scrollLeft + insetPx;
      sliderStyle.value = {
        left: left + 'px',
        width: width + 'px',
      };
    }
  });
}

onMounted(() => {
  updateSlider();
});

watch(activeIndex, (newVal, oldVal) => {
  lastIndex.value = oldVal;
  updateSlider();
});

if (typeof window !== 'undefined') {
  window.addEventListener('resize', updateSlider);
}

const transitionName = computed(() =>
  activeIndex.value > lastIndex.value ? 'tab-slide-left' : 'tab-slide-right'
);

function onTabsHeaderKeydown(e: KeyboardEvent) {
  if (e.key === 'ArrowRight') {
    setActiveTab((activeIndex.value + 1) % props.tabs.length);
    e.preventDefault();
  } else if (e.key === 'ArrowLeft') {
    setActiveTab(
      (activeIndex.value - 1 + props.tabs.length) % props.tabs.length
    );
    e.preventDefault();
  }
}
</script>

<style scoped lang="scss">
.tabs-container {
  height: 100%;
  position: relative;
  font-family: 'Microsoft YaHei', sans-serif;
  background: var(--background-surface);
  padding: 1rem;
  box-shadow:
    var(--shadow-dimension-md),
    var(--shadow-neon-primary),
    0 0 30px rgba(143, 51, 255, 0.1);
  border: 1px solid var(--interactive-primary);
  display: flex;
  flex-direction: column;
  /* 确保容器不会压缩子元素 */
  min-width: 0;
  overflow: hidden;
}

.tabs-header {
  display: flex;
  gap: 0.2rem;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
  flex-wrap: nowrap;
  align-items: flex-end;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.5rem 0 0;
  padding-bottom: 6px;
  /* 隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* 防止子元素被压缩，确保水平滚动正常工作 */
  flex-shrink: 0;
  min-width: 0;
  width: 100%;
  /* 平滑滚动 */
  scroll-behavior: smooth;
}

.tab-slider {
  position: absolute;
  bottom: 0;
  height: 2px;
  border-radius: 1px;
  background: linear-gradient(
    90deg,
    var(--interactive-primary) 0%,
    #6a3093 40%,
    #8f33ff 70%,
    var(--magic-gold) 100%
  );
  box-shadow:
    0 0 8px 2px var(--interactive-primary-translucent),
    0 0 16px 4px var(--magic-gold),
    0 0 12px 2px #8f33ff;
  transition:
    left 0.35s cubic-bezier(0.4, 0, 0.2, 1),
    width 0.35s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 3;
  pointer-events: none;
}

.tab-button {
  position: relative;
  padding: 0.7rem 1.2rem;
  background: var(--background-elevated);
  border: none;
  border-radius: var(--border-radius-sm);
  cursor: pointer;
  font-size: 1.05rem;
  font-weight: 700;
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  text-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
  overflow: visible;
  z-index: 1;
  white-space: nowrap;
  min-width: max-content;
  /* 防止按钮被压缩 */
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  /* 确保按钮内容不会被截断 */
  width: auto;

  &:hover {
    color: var(--text-primary);
    background: var(--background-floating);
    box-shadow: var(--shadow-neon-primary);
    transform: translateY(-2px);
  }
  &.active {
    color: var(--text-primary);
    background: linear-gradient(
      135deg,
      var(--interactive-primary),
      #6a3093,
      #8f33ff
    );
    transform: translateY(-3px);
  }
}

.tabs-content {
  flex: 1 1 0;
  min-height: 0;
  overflow: hidden;
  padding: 0;
  background: var(--background-elevated);
  border-radius: var(--border-radius-md);
  border: 1px solid var(--interactive-primary);
  box-shadow:
    inset 0 0 15px rgba(0, 0, 0, 0.07),
    inset 0 0 30px var(--interactive-primary-translucent);
  position: relative;
  z-index: 2;
  width: 100%;
}

.tab-panels {
  position: relative;
  height: 100%;
  overflow: auto;
}

.tab-panel {
  position: relative;
  z-index: 3;
}

/* 选项卡内容切换动画 */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition:
    opacity 0.3s,
    transform 0.3s;
}
.tab-fade-enter-from {
  opacity: 0;
  transform: translateY(20px);
}
.tab-fade-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

/* 新增滑动动画 */
.tab-slide-left-enter-active,
.tab-slide-left-leave-active,
.tab-slide-right-enter-active,
.tab-slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: absolute;
  width: 100%;
}
.tab-slide-left-enter-from {
  opacity: 0;
  transform: translateX(40px);
}
.tab-slide-left-leave-to {
  opacity: 0;
  transform: translateX(-40px);
}
.tab-slide-right-enter-from {
  opacity: 0;
  transform: translateX(-40px);
}
.tab-slide-right-leave-to {
  opacity: 0;
  transform: translateX(40px);
}
</style>

