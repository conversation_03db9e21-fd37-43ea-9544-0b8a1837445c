{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@types/maxmind": "^2.0.5", "@vueuse/core": "^13.2.0", "cropperjs": "^1.6.1", "dayjs": "^1.11.13", "maxmind": "^4.3.27", "motion-v": "1.1.0-alpha.1", "nuxt": "^3.16.2", "pinia": "^3.0.2", "socket.io-client": "^4.8.1", "ua-parser-js": "^2.0.3", "vue": "latest"}, "devDependencies": {"@inspira-ui/plugins": "^0.0.1", "@nuxt/eslint": "^1.3.0", "@nuxtjs/color-mode": "^3.5.2", "@pinia/nuxt": "^0.11.0", "@vueuse/nuxt": "^13.1.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "pinia-plugin-persistedstate": "^4.2.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "sass": "^1.87.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3"}}