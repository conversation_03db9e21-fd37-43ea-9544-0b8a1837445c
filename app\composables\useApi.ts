// 初始化各模块API
const authApi = useAuthApi(); // 认证相关API
const captchaApi = useCaptchaApi(); // 验证码相关API
const userApi = useUserApi(); // 用户相关API
const photosApi = usePhotosApi(); // 照片相关API
const postsApi = usePostsApi(); // 帖子相关API
const searchApi = useSearchApi(); // 搜索相关API

/**
 * AI聊天接口
 * @param message - 用户发送的消息内容
 * @returns Promise - 返回API调用结果
 */
const chatApi = async (message: string) => {
  try {
    // 调用流式聊天接口
    const res = await useRequest('/chat/stream', {
      method: 'POST',
      body: {
        message, // 请求体包含用户消息
      },
    });
    // TODO: 需要返回处理后的结果
  } catch (err) {
    throw err; // 抛出错误给调用方处理
  }
};

// 导出组合式API
export const useApi = () => {
  return {
    ...authApi, // 展开认证API
    ...captchaApi, // 展开验证码API
    ...userApi, // 展开用户API
    ...photosApi, // 展开照片API
    ...postsApi, // 展开帖子API
    ...searchApi, // 展开搜索API
    /** AI聊天接口 */
    chatApi, // 添加AI聊天接口
  };
};

