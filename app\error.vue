<script setup lang="ts">
// 导入Nuxt错误类型
import type { NuxtError } from '#app';

// 定义组件props，接收错误对象
const { error } = defineProps({
  error: Object as () => NuxtError,
});

// 错误处理函数
const handleError = () => {
  if (error) {
    // 检查错误消息是否包含/auth路径
    if (error.message.includes('/mobile/auth')) {
      // 清除错误并重定向到登录页面
      clearError({ redirect: '/mobile/auth/login/email' });
    } else if (error.message.includes('/auth')) {
      // 清除错误并重定向到登录页面
      clearError({ redirect: '/auth/login/email' });
    } else {
      // 清除错误并重定向到首页
      clearError({ redirect: '/' });
    }
  }
};

// 检查404状态码
if (error?.statusCode === 404) {
  console.log('Page not found');
}

// 执行错误处理
handleError();
</script>

<!-- 空模板，不需要渲染内容 -->
<template></template>
