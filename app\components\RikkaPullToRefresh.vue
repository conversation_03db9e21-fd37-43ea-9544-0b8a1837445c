<!-- 下拉刷新组件 -->
<template>
  <div
    class="pull-refresh-container"
    @touchstart="handleTouchStartSafe"
    @touchmove="handleTouchMoveSafe"
    @touchend="handleTouchEnd"
    @touchcancel="handleTouchEnd"
    ref="containerRef"
  >
    <!-- 刷新头部 -->
    <div
      class="pull-refresh-head"
      :style="{
        transform: `translateY(${headPosition}px)`,
        height: `${props.headHeight}px`,
      }"
    >
      <div class="refresh-indicator">
        <div
          class="spinner"
          :style="{
            opacity: spinnerOpacity,
            transform: spinnerTransform,
          }"
          aria-hidden="true"
        ></div>
        <div
          class="refresh-text"
          :style="{ color: textColor }"
          aria-live="polite"
          :aria-label="statusText"
        >
          {{ statusText }}
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div
      class="pull-refresh-content"
      :style="{ transform: `translateY(${contentPosition}px)` }"
      ref="contentRef"
    >
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
/**
 * 下拉刷新组件
 * 提供移动端下拉刷新功能，支持自定义阈值和样式
 */

// 状态常量枚举
const STATUS = {
  PULLING: 'pulling',
  READY_TO_REFRESH: 'ready',
  REFRESHING: 'refreshing',
} as const;

type StatusType = (typeof STATUS)[keyof typeof STATUS];

// 组件属性接口定义
interface PullToRefreshProps {
  /** 触发刷新的下拉阈值（像素） */
  threshold?: number;
  /** 头部高度（像素） */
  headHeight?: number;
  /** 是否禁用下拉刷新 */
  disabled?: boolean;
  /** 阻力系数，控制下拉的阻力感 */
  resistance?: number;
}

// 组件属性
const props = withDefaults(defineProps<PullToRefreshProps>(), {
  threshold: 200,
  headHeight: 70,
  disabled: false,
  resistance: 0.6,
});

// 组件事件
const emit = defineEmits<{
  /** 开始下拉时触发 */
  pullStart: [];
  /** 下拉过程中触发 */
  pulling: [distance: number];
  /** 释放刷新时触发，父组件需要调用done()来完成刷新 */
  refresh: [done: () => void];
  /** 刷新完成时触发 */
  refreshComplete: [];
}>();

// DOM 引用
const containerRef = ref<HTMLElement | null>(null);
const contentRef = ref<HTMLElement | null>(null);

// 响应式状态
const status = ref<StatusType>(STATUS.PULLING);
const startY = ref(0);
const currentY = ref(0);
const headPosition = ref(-props.headHeight);
const contentPosition = ref(0);
const spinnerOpacity = ref(0);
const spinnerTransform = ref('scale(0.8) rotate(0deg)');
const textColor = ref('#e6e6fa');
const isRefreshing = ref(false);
const isPulling = ref(false); // 添加下拉状态标记
let resetTimer: number | null = null;
let lastMoveTime = 0; // 用于节流

// 计算属性
const statusText = computed(() => {
  switch (status.value) {
    case STATUS.PULLING:
      return '下拉刷新';
    case STATUS.READY_TO_REFRESH:
      return '释放刷新';
    case STATUS.REFRESHING:
      return '刷新中...';
    default:
      return '下拉刷新';
  }
});

// 检查元素是否可滚动且不在顶部
const isScrollableAndNotAtTop = (element: Element): boolean => {
  const { scrollTop, scrollHeight, clientHeight } = element;
  return scrollHeight > clientHeight && scrollTop > 5;
};

// 检查是否可以开始下拉
const canStartPull = (): boolean => {
  if (props.disabled || isRefreshing.value) return false;

  // 检查页面是否在顶部 - 使用更准确的滚动检测
  const scrollTop =
    window.pageYOffset ||
    document.documentElement.scrollTop ||
    document.body.scrollTop ||
    0;
  if (scrollTop > 5) return false;

  // 检查内容容器及其子元素是否在顶部
  const contentElement = contentRef.value;
  if (contentElement) {
    // 检查内容容器本身
    if (isScrollableAndNotAtTop(contentElement)) return false;

    // 只检查直接的可滚动子元素，避免深度遍历影响性能
    // 特别针对瀑布流等组件的滚动容器
    const directScrollableElements = contentElement.querySelectorAll(
      ':scope > .waterfall-container, :scope > .scroll-container, :scope > [style*="overflow"]'
    );
    for (const element of directScrollableElements) {
      if (isScrollableAndNotAtTop(element)) return false;
    }
  }

  return true;
};

// 计算下拉距离（带阻力效果）
const calculatePullDistance = (diff: number): number => {
  return Math.min(diff * props.resistance, props.threshold * 1.5);
};

// 更新视觉状态
const updateVisualState = (pullDistance: number): void => {
  const progress = Math.min(pullDistance / props.threshold, 1);

  if (pullDistance > props.threshold) {
    status.value = STATUS.READY_TO_REFRESH;
    textColor.value = '#ffd700';
    spinnerOpacity.value = 1;
    spinnerTransform.value = 'scale(1) rotate(180deg)';
  } else {
    status.value = STATUS.PULLING;
    textColor.value = '#e6e6fa';
    spinnerOpacity.value = progress;
    spinnerTransform.value = `scale(${0.8 + 0.2 * progress}) rotate(${180 * progress}deg)`;
  }

  emit('pulling', pullDistance);
};

// 触摸开始处理
const handleTouchStart = (e: TouchEvent): void => {
  // 只在页面顶部时记录触摸开始位置
  if (!canStartPull()) return;

  startY.value = e.touches[0]?.clientY || 0;
  currentY.value = startY.value;
  isPulling.value = false; // 初始不标记为下拉状态，等待确认下拉方向

  // 重置位置
  headPosition.value = -props.headHeight;
  contentPosition.value = 0;

  // 移除过渡效果以实现流畅拖拽
  if (containerRef.value) {
    containerRef.value.style.transition = 'none';
  }

  emit('pullStart');
};

// 触摸移动处理（添加节流优化性能）
const handleTouchMove = (e: TouchEvent): void => {
  if (isRefreshing.value || props.disabled) return;

  // 节流处理，避免过于频繁的更新
  const now = Date.now();
  if (now - lastMoveTime < 16) return; // 约60fps
  lastMoveTime = now;

  currentY.value = e.touches[0]?.clientY || 0;
  const diff = currentY.value - startY.value;

  // 如果是向下拉动且距离足够，开始下拉刷新
  if (diff > 5 && !isPulling.value) {
    // 再次检查是否可以开始下拉（防止内容滚动时误触发）
    if (!canStartPull()) {
      return;
    }

    // 标记为下拉状态
    isPulling.value = true;
  }

  // 一旦进入下拉状态，就完全阻止所有默认行为和事件传播
  if (isPulling.value) {
    // 强制阻止默认滚动行为和事件冒泡
    e.preventDefault();
    e.stopPropagation();
    e.stopImmediatePropagation();

    // 阻止触摸事件传递到子元素
    if (contentRef.value) {
      contentRef.value.style.pointerEvents = 'none';
    }

    // 只有向下拉动时才更新视觉效果
    if (diff > 0) {
      // 计算下拉距离
      const pullDistance = calculatePullDistance(diff);

      // 更新位置
      contentPosition.value = pullDistance;
      headPosition.value = pullDistance - props.headHeight;

      // 更新视觉状态
      updateVisualState(pullDistance);
    } else {
      // 向上滑动时，保持在初始位置，完全阻止内部滚动
      contentPosition.value = 0;
      headPosition.value = -props.headHeight;
      status.value = STATUS.PULLING;
      spinnerOpacity.value = 0;
    }
  }
};

// 触摸结束处理
const handleTouchEnd = (): void => {
  if (isRefreshing.value || props.disabled) return;

  // 恢复内容区域的指针事件
  if (contentRef.value) {
    contentRef.value.style.pointerEvents = '';
  }

  // 只有在下拉状态下才处理触摸结束
  if (isPulling.value) {
    const diff = currentY.value - startY.value;
    const pullDistance = calculatePullDistance(Math.max(0, diff)); // 确保距离不为负

    // 使用计算后的距离进行判断，保持一致性
    if (pullDistance > props.threshold) {
      startRefresh();
    } else {
      reset();
    }
  }

  // 重置下拉状态
  isPulling.value = false;
};

// 开始刷新
const startRefresh = (): void => {
  if (isRefreshing.value) return;

  isRefreshing.value = true;
  status.value = STATUS.REFRESHING;
  headPosition.value = 0;
  contentPosition.value = props.headHeight;
  spinnerOpacity.value = 1;
  spinnerTransform.value = 'scale(1)';

  // 添加平滑过渡
  if (containerRef.value) {
    containerRef.value.style.transition = 'transform 0.3s ease';
  }

  // 触发刷新事件，传递done回调函数
  emit('refresh', () => {
    // 刷新完成后复位
    reset();
  });
};

// 复位状态
const reset = (): void => {
  // 清除之前的定时器
  if (resetTimer) {
    clearTimeout(resetTimer);
    resetTimer = null;
  }

  // 恢复内容区域的指针事件
  if (contentRef.value) {
    contentRef.value.style.pointerEvents = '';
  }

  // 重置下拉状态
  isPulling.value = false;
  contentPosition.value = 0;
  headPosition.value = -props.headHeight;

  // 添加过渡效果
  if (containerRef.value) {
    containerRef.value.style.transition = 'transform 0.3s ease';
  }

  // 短暂延迟后恢复初始状态
  resetTimer = setTimeout(() => {
    status.value = STATUS.PULLING;
    spinnerOpacity.value = 0;
    isRefreshing.value = false;

    // 移除过渡效果
    if (containerRef.value) {
      containerRef.value.style.transition = '';
    }

    emit('refreshComplete');
  }, 300) as unknown as number;
};

// 修复触摸事件的类型安全问题
const handleTouchStartSafe = (e: TouchEvent): void => {
  if (props.disabled) return;
  if (!e.touches || e.touches.length === 0) return;

  // 确保只处理单点触摸
  if (e.touches.length > 1) {
    isPulling.value = false;
    return;
  }

  handleTouchStart(e);
};

const handleTouchMoveSafe = (e: TouchEvent): void => {
  if (props.disabled) return;
  if (!e.touches || e.touches.length === 0) return;

  // 多点触摸时停止下拉
  if (e.touches.length > 1) {
    isPulling.value = false;
    return;
  }

  handleTouchMove(e);
};

// 暴露组件方法给父组件
defineExpose({
  /** 手动触发刷新 */
  refresh: startRefresh,
  /** 手动重置状态 */
  reset,
  /** 获取当前状态 */
  getStatus: () => status.value,
  /** 获取是否正在刷新 */
  isRefreshing: () => isRefreshing.value,
});

// 清理函数
const cleanup = (): void => {
  if (resetTimer) {
    clearTimeout(resetTimer);
    resetTimer = null;
  }

  isPulling.value = false;
  isRefreshing.value = false;

  if (containerRef.value) {
    containerRef.value.style.transition = '';
  }
};

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});

// 确保组件重置时清除定时器
onMounted(() => {
  cleanup();
});
</script>

<style scoped lang="scss">
.pull-refresh-container {
  position: relative;
  height: 100%;
  overflow: hidden;
  touch-action: pan-y;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  -webkit-user-select: none;

  // 确保在移动设备上有更好的性能
  will-change: transform;
  transform: translateZ(0);
}

.pull-refresh-head {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.refresh-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 8px 16px;
}

.refresh-text {
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  white-space: nowrap;
  letter-spacing: 0.5px;
}

.spinner {
  width: 28px;
  height: 28px;
  border: 2.5px solid rgba(255, 215, 0, 0.2);
  border-radius: 50%;
  border-top-color: #ffd700;
  border-right-color: #ffd700;
  position: relative;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // 硬件加速
  will-change: transform;
  transform: translateZ(0);

  // 添加旋转动画（从瀑布流组件移植）
  animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.265, 1.55) infinite;
}

.spinner::after {
  content: '';
  position: absolute;
  top: -2.5px;
  left: -2.5px;
  right: -2.5px;
  bottom: -2.5px;
  border-radius: 50%;
  box-shadow:
    0 0 8px rgba(255, 215, 0, 0.4),
    0 0 16px rgba(255, 215, 0, 0.2),
    inset 0 0 8px rgba(255, 215, 0, 0.1);
  // 添加光晕动画（从瀑布流组件移植）
  animation: glow 2s ease-in-out infinite;
}

.pull-refresh-content {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // 优化滚动性能
  will-change: transform;
  transform: translateZ(0);

  // 移动端优化
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

// 动画关键帧（从瀑布流组件移植）
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .refresh-text {
    font-size: 13px;
  }

  .spinner {
    width: 24px;
    height: 24px;
    border-width: 2px;
  }

  .refresh-indicator {
    gap: 8px;
    padding: 6px 12px;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .pull-refresh-head {
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.1) 100%
    );
  }

  .refresh-indicator {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
  }
}

// 减少动画的用户偏好
@media (prefers-reduced-motion: reduce) {
  .spinner {
    animation: none;
  }

  .spinner::after {
    animation: none;
  }

  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
  }
}
</style>

