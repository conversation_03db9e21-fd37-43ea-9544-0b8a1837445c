<template>
  <AuroraBackground class="auth-background">
    <div class="auth-layout">
      <div class="box">
        <slot />
      </div>
    </div>
  </AuroraBackground>
</template>

<script lang="ts" setup></script>

<style lang="scss" scoped>
.auth-layout {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  > .box {
    background: var(--neutral-disabled);
    backdrop-filter: blur(10px); /* 毛玻璃效果 */
    border: var(--border-focus-ring);
    box-shadow: var(--shadow-neon-primary);
    padding: 3rem 5rem;
    border-radius: var(--border-radius-md);
  }
}
</style>
