<template>
  <div class="rikka-banner" :class="theme">
    <div class="decoration" ref="decoration"></div>

    <!-- 魔法阵背景 -->
    <div class="magic-circle" v-if="theme === 'magic'"></div>

    <!-- 哥特式装饰 -->
    <div class="gothic-decoration" v-if="theme === 'gothic'">
      <div class="gothic-element"></div>
      <div class="gothic-element"></div>
    </div>

    <!-- 左边框装饰元素 -->
    <div class="border-element left"></div>
    <!-- 右边框装饰元素 -->
    <div class="border-element right"></div>

    <!-- 标题 -->
    <h1 class="banner-title">{{ title }}</h1>
    <!-- 副标题 -->
    <p class="banner-subtitle">{{ subtitle }}</p>
  </div>
</template>

<script setup lang="ts">
// 定义组件的props接口
interface Props {
  title?: string;
  subtitle?: string;
  theme?: 'magic' | 'gothic' | 'simple';
}

// 使用默认值定义props
const props = withDefaults(defineProps<Props>(), {
  title: '精选推荐',
  subtitle: '发现最受欢迎的视觉盛宴',
  theme: 'magic',
});

// 定义装饰元素的引用
const decoration = ref<HTMLDivElement | null>(null);
</script>

<style scoped lang="scss">
.rikka-banner {
  position: relative;
  width: 100%;
  height: var(--banner-height);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--background-gradient-1);
  overflow: hidden;
  padding: var(--banner-padding);
  text-align: center;
  border: var(--banner-border-width) solid
    var(--interactive-primary-translucent);
  box-shadow:
    var(--shadow-dimension-md),
    inset 0 0 1.25rem rgba(124, 58, 237, 0.2);
  border-radius: var(--banner-border-radius);
  margin-bottom: 2rem;

  .banner-title {
    font-size: 2.8rem;
    font-weight: bold;
    color: var(--text-primary);
    text-shadow: var(--shadow-neon-primary);
    position: relative;
    letter-spacing: 0.1875rem;
  }

  .banner-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    max-width: 43.75rem;
    position: relative;
  }

  // 魔法阵样式
  .magic-circle {
    position: absolute;
    width: var(--magic-circle-size);
    height: var(--magic-circle-size);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0.3;
    background:
      radial-gradient(
        circle,
        transparent 60%,
        var(--interactive-primary-translucent) 100%
      ),
      conic-gradient(
        rgba(124, 58, 237, 0.1) 0%,
        rgba(167, 139, 250, 0.2) 25%,
        rgba(124, 58, 237, 0.1) 50%,
        rgba(167, 139, 250, 0.2) 75%,
        rgba(124, 58, 237, 0.1) 100%
      );
    border-radius: 50%;
    border: 0.125rem dashed rgba(199, 210, 254, 0.3);
    animation: rotate 30s linear infinite;

    &::before {
      content: '';
      position: absolute;
      top: 1.25rem;
      left: 1.25rem;
      right: 1.25rem;
      bottom: 1.25rem;
      border: 0.0625rem solid var(--neutral-divider);
      border-radius: 50%;
    }

    &::after {
      content: '✦';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 2.5rem;
      color: var(--text-accent);
      opacity: 0.5;
    }
  }

  // 哥特式装饰
  .gothic-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    display: flex;
    justify-content: space-between;
    padding: 0 1.875rem;

    .gothic-element {
      height: 100%;
      width: 1.25rem;
      background: linear-gradient(
        to bottom,
        transparent 0%,
        var(--interactive-primary-translucent) 10%,
        transparent 20%,
        var(--interactive-primary-translucent) 30%,
        transparent 40%,
        var(--interactive-primary-translucent) 50%,
        transparent 60%,
        var(--interactive-primary-translucent) 70%,
        transparent 80%,
        var(--interactive-primary-translucent) 90%,
        transparent 100%
      );

      &::before,
      &::after {
        content: '†';
        position: absolute;
        font-size: 1.8rem;
        color: var(--text-link);
        opacity: 0.7;
      }

      &::before {
        top: 1.25rem;
      }

      &::after {
        bottom: 1.25rem;
      }
    }
  }

  // 装饰边框
  .border-element {
    position: absolute;

    &.top,
    &.bottom {
      height: var(--banner-border-width);
      left: 1.25rem;
      right: 1.25rem;
      background: linear-gradient(
        90deg,
        transparent,
        var(--interactive-primary),
        transparent
      );
    }

    &.left,
    &.right {
      width: var(--banner-border-width);
      top: 1.25rem;
      bottom: 1.25rem;
      background: linear-gradient(
        0deg,
        transparent,
        var(--interactive-primary),
        transparent
      );
    }

    &.top {
      top: var(--banner-element-spacing);
    }

    &.bottom {
      bottom: var(--banner-element-spacing);
    }

    &.left {
      left: var(--banner-element-spacing);
    }

    &.right {
      right: var(--banner-element-spacing);
    }
  }

  // 星光效果
  .decoration {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .star {
      position: absolute;
      background: var(--text-primary);
      border-radius: 50%;
      animation: twinkle 3s infinite ease-in-out;
    }
  }

  @keyframes twinkle {
    0%,
    100% {
      opacity: 0.2;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1.2);
    }
  }

  @keyframes rotate {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(360deg);
    }
  }

  // 响应式设计
  @media (max-width: 48rem) {
    // 768px
    --banner-height: 10rem; // 160px
    --magic-circle-size: 11.25rem; // 180px

    .banner-title {
      font-size: 2.2rem;
    }

    .banner-subtitle {
      font-size: 1rem;
    }
  }

  @media (max-width: 30rem) {
    // 480px
    --banner-height: 8.75rem; // 140px

    .banner-title {
      font-size: 1.8rem;
    }

    .banner-subtitle {
      font-size: 0.9rem;
    }

    .gothic-decoration {
      padding: 0 0.9375rem; // 15px
    }
  }
}
</style>
