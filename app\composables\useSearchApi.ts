/**
 * 搜索相关API的组合式函数
 * 提供与搜索功能相关的API接口封装
 */

/**
 * 获取搜索框热词列表
 * @returns 返回Promise，解析后包含热词列表的响应数据
 * @throws 当请求失败时抛出错误
 */
const getSearchHotwords = async () => {
  try {
    // 调用通用请求方法获取热词数据
    const { data } = await useRequest<ApiResponse<string[]>>(
      '/search/hotKeyWords', // 热词接口路径
      {
        token: false, // 不需要认证token
      }
    );
    return data;
  } catch (err) {
    throw err;
  }
};

/**
 * 导出搜索相关API
 * @returns 返回包含所有搜索API方法的对象
 */
export const useSearchApi = () => {
  return {
    /** 获取搜索框热词列表 */
    getSearchHotwords,
  };
};

