<template>
  <div class="page-container">
    <Head>
      <Title>用户搜索-{{ key }}</Title>
    </Head>
    <RikkaUsersGrid
      :users="usersArray"
      :card-width="300"
      :gap="20"
      :loading="usersLoading"
      :has-more="hasMoreUsers"
      @load-more="handleLoadMoreUsers"
    />
  </div>
</template>
<script lang="ts" setup>
definePageMeta({
  layout: 'search',
});

const route = useRoute();
const key = computed(() => route.query.key as string);

const usersArray = ref<UsersList[]>([]);
const usersLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreUsers = computed(
  () => usersPaginated.value.page < usersPaginated.value.totalPage
);
// 分页信息
const usersPaginated = ref<Paginated<UserSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'visitedCount',
  sortOrder: 'asc',
});
const getUserList = async (query?: UserListQuery) => {
  try {
    const { list, ...res } = await useApi().getUserList(query);
    usersArray.value.push(...list);
    usersPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

const handleLoadMoreUsers = async () => {
  if (usersLoading.value || !hasMoreUsers.value) {
    return;
  }
  usersLoading.value = true;

  await getUserList({
    page: usersPaginated.value.page + 1,
    pageSize: 20,
    sortField: 'visitedCount',
    keyword: key.value,
  });
  usersLoading.value = false;
};

watch(key, async () => {
  usersArray.value = [];
  await getUserList({
    page: 1,
    pageSize: 20,
    sortField: 'visitedCount',
    keyword: key.value,
  });
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
