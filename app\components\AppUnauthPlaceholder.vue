<template>
  <div class="placeholder-container">
    <!-- 占位符内容的包裹容器，包含头像、文本、按钮及装饰性元素 -->
    <div class="placeholder-content">
      <!-- 头像占位符，采用小鸟游六花风格的视觉设计 -->
      <div class="avatar-placeholder">
        <!-- 顶部绷带 -->
        <div class="band top"></div>
        <!-- 底部绷带 -->
        <div class="band bottom"></div>
        <!-- 眼罩 -->
        <div class="eyepatch">
          <!-- 眼罩内部的小圆 -->
          <div class="eyepatch-inner"></div>
          <!-- 眼罩的带子 -->
          <div class="eyepatch-strap"></div>
        </div>
      </div>

      <!-- 文本占位符，用于显示未登录时的提示文本 -->
      <div class="text-placeholder">
        <!-- 长文本行 -->
        <div class="line"></div>
        <!-- 中等长度文本行 -->
        <div class="line medium"></div>
        <!-- 短文本行 -->
        <div class="line short"></div>
      </div>

      <!-- 登录按钮，点击后跳转到登录页面 -->
      <button class="login-btn" @click="$router.push('/auth/login/email')">
        进入邪王真眼的世界
      </button>

      <!-- 装饰性元素，用于美化页面 -->
      <div class="decoration">
        <!-- 大魔法阵 -->
        <div class="magic-circle"></div>
        <!-- 中魔法阵 -->
        <div class="magic-circle small"></div>
        <!-- 小魔法阵 -->
        <div class="magic-circle tiny"></div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 这里可以添加JavaScript或TypeScript逻辑代码，当前为空
</script>

<style lang="scss" scoped>
/* 占位符容器样式 */
.placeholder-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem;
  box-sizing: border-box;
  background: rgba(22, 12, 46, 0.5);
  border-radius: 1rem;
  min-height: 30rem;

  /* 占位符内容样式 */
  .placeholder-content {
    max-width: 40rem;
    width: 100%;
    text-align: center;
    position: relative;

    /* 头像占位符样式 - 小鸟游六花风格 */
    .avatar-placeholder {
      width: 12rem;
      height: 12rem;
      margin: 0 auto 1.8rem;
      border-radius: 50%;
      background: linear-gradient(135deg, #6a0dad 0%, #4a0072 100%);
      position: relative;
      overflow: hidden;
      box-shadow: 0 0.5rem 1.5rem rgba(106, 13, 173, 0.4);
      animation: float 3s ease-in-out infinite;

      /* 绷带样式 */
      .band {
        position: absolute;
        background: rgba(255, 255, 255, 0.9);
        height: 0.8rem;
        width: 100%;
        z-index: 2;

        &.top {
          top: 3.5rem;
        }

        &.bottom {
          top: 5.5rem;
        }
      }

      /* 眼罩样式 */
      .eyepatch {
        position: absolute;
        width: 5rem;
        height: 5rem;
        background: #1a1a1a;
        border-radius: 50%;
        left: 1.5rem;
        top: 3.5rem;
        z-index: 3;
        box-shadow: 0 0 0 0.3rem #ffd700;

        /* 眼罩内部的小圆样式 */
        .eyepatch-inner {
          position: absolute;
          width: 3rem;
          height: 3rem;
          background: #000;
          border-radius: 50%;
          left: 1rem;
          top: 1rem;
        }

        /* 眼罩的带子样式 */
        .eyepatch-strap {
          position: absolute;
          width: 7rem;
          height: 1rem;
          background: #1a1a1a;
          top: 2.5rem;
          left: 4.5rem;
          border-radius: 0.5rem;
          box-shadow: 0 0 0 0.2rem #ffd700;
        }
      }
    }

    /* 文本占位符样式 */
    .text-placeholder {
      margin-bottom: 2.2rem;

      /* 文本行样式 */
      .line {
        height: 1.2rem;
        background: linear-gradient(to right, #5a2d8c, #7e4bb4);
        border-radius: 0.6rem;
        margin-bottom: 1rem;
        position: relative;
        overflow: hidden;

        /* 闪烁动画效果 */
        &::after {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          animation: shimmer 1.5s infinite;
        }

        &.medium {
          width: 80%;
          margin-left: auto;
          margin-right: auto;
        }

        &.short {
          width: 60%;
          margin-left: auto;
          margin-right: auto;
        }
      }
    }

    /* 登录按钮样式 */
    .login-btn {
      max-width: 28rem;
      padding: 1rem 1.8rem;
      background: linear-gradient(135deg, #a45deb 0%, #6a0dad 100%);
      color: white;
      border: none;
      border-radius: 3rem;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 0.5rem 1.5rem rgba(106, 13, 173, 0.4);
      position: relative;
      overflow: hidden;
      z-index: 1;

      /* 图标样式 */
      i {
        margin-right: 0.8rem;
      }

      /* 鼠标悬停效果 */
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3),
          transparent
        );
        transition: 0.5s;
        z-index: -1;
      }

      &:hover {
        transform: translateY(-0.3rem);
        box-shadow: 0 0.8rem 2rem rgba(106, 13, 173, 0.6);

        &::before {
          left: 100%;
        }
      }

      /* 按钮激活效果 */
      &:active {
        transform: translateY(0.1rem);
      }
    }

    /* 装饰性元素样式 */
    .decoration {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
      z-index: 0;

      /* 魔法阵样式 */
      .magic-circle {
        position: absolute;
        border: 0.2rem solid rgba(255, 215, 0, 0.4);
        border-radius: 50%;
        animation: pulse 4s infinite ease-in-out;

        &.small {
          width: 25rem;
          height: 25rem;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation-delay: 1s;
        }

        &.tiny {
          width: 18rem;
          height: 18rem;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          animation-delay: 2s;
        }
      }
    }
  }
}

/* 浮动动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-1rem);
  }
}

/* 闪烁动画 */
@keyframes shimmer {
  100% {
    left: 100%;
  }
}

/* 脉冲动画 */
@keyframes pulse {
  0%,
  100% {
    opacity: 0.2;
    transform: translate(-50%, -50%) scale(0.95);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1.05);
  }
}
</style>

