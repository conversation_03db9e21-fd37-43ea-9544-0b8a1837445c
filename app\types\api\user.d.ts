/**
 * 当前登录用户信息
 * @property {string} uid - 用户唯一标识
 * @property {string} avatar - 用户头像URL，格式为完整的HTTP/HTTPS链接
 * @property {string} background - 用户背景图URL，格式为完整的HTTP/HTTPS链接
 * @property {string} nickname - 用户昵称，2-20个字符，支持中英文、数字和下划线
 * @property {'male' | 'female' | 'hidden'} gender - 用户性别，可选值为'male'(男)、'female'(女)或'hidden'(隐藏)
 * @property {string} birthday - 用户生日，格式为YYYY-MM-DD
 * @property {string} bio - 用户个人简介，最大长度500字符
 * @property {number} totalDownloads - 用户上传图片被下载总次数，非负整数
 * @property {number} favoriteContentsCount - 用户收藏的帖子数量，非负整数
 * @property {number} favoritePhotosCount - 用户收藏的图片数量，非负整数
 * @property {number} followersCount - 粉丝数量，非负整数
 * @property {number} followingsCount - 关注数量，非负整数
 * @property {number} likeContentsCount - 用户点赞的帖子数量，非负整数
 * @property {number} likePhotosCount - 用户点赞的图片数量，非负整数
 * @property {string} [email] - 可选，用户绑定邮箱，符合标准邮箱格式
 * @property {string} [phone] - 可选，用户绑定手机号，11位数字
 * @property {Array<{provider: string, uid: string}>} socialLogins - 社交账号登录信息数组，包含provider(平台)和uid(平台用户ID)
 * @property {string} createTime - 账号创建时间，ISO 8601格式(YYYY-MM-DDTHH:mm:ss.sssZ)
 */
interface Auth {
  uid: string;
  avatar: string;
  background: string;
  nickname: string;
  gender: 'male' | 'female' | 'hidden';
  birthday: string;
  bio: string;
  totalDownloads: number;
  photosCount: number;
  contentsCount: number;
  favoriteContentsCount: number;
  favoritePhotosCount: number;
  followersCount: number;
  followingsCount: number;
  likeContentsCount: number;
  likePhotosCount: number;
  email?: string;
  phone?: string;
  socialLogins: any[];
  createTime: string;
}

/**
 * 其他用户公开信息
 * @property {string} uid - 用户唯一标识
 * @property {string} avatar - 用户头像URL，格式为完整的HTTP/HTTPS链接
 * @property {string} background - 用户背景图URL，格式为完整的HTTP/HTTPS链接
 * @property {string} nickname - 用户昵称，2-20个字符
 * @property {'male' | 'female' | 'hidden'} gender - 用户性别
 * @property {string} birthday - 用户生日，格式为YYYY-MM-DD
 * @property {string} bio - 用户个人简介，最大长度500字符
 * @property {number} totalDownloads - 用户上传图片被下载总次数，非负整数
 * @property {boolean} isFollowing - 当前用户是否已关注该用户
 * @property {boolean} isFans - 该用户是否是当前用户的粉丝
 * @property {number} followersCount - 粉丝数量，非负整数
 * @property {number} followingsCount - 关注数量，非负整数
 * @property {number} visitedCount - 该用户主页被访问次数，非负整数
 * @property {number} likePhotosCount - 用户点赞的图片数量，非负整数
 * @property {number} favoritePhotosCount - 用户收藏的图片数量，非负整数
 * @property {number} likeContentsCount - 用户点赞的帖子数量，非负整数
 * @property {number} favoriteContentsCount - 用户收藏的帖子数量，非负整数
 * @property {string} createTime - 账号创建时间，ISO 8601格式
 * @property {string} lastLoginTime - 最后登录时间，ISO 8601格式
 */
interface OtherUserInfo {
  uid: string;
  avatar: string;
  background: string;
  nickname: string;
  gender: string;
  birthday: string;
  bio: string;
  totalDownloads: number;
  isFollowing: boolean;
  isFans: boolean;
  photosCount: number;
  contentsCount: number;
  followersCount: number;
  followingsCount: number;
  visitedCount: number;
  likePhotosCount: number;
  favoritePhotosCount: number;
  likeContentsCount: number;
  favoriteContentsCount: number;
  createTime: string;
  lastLoginTime: string;
}

/**
 * 用户隐私设置
 * @property {boolean} showFollowList - 是否公开关注列表，true表示公开
 * @property {boolean} showFansList - 是否公开粉丝列表，true表示公开
 * @property {boolean} showFavoritePhotoList - 是否公开收藏的图片列表，true表示公开
 * @property {boolean} showLikePhotoList - 是否公开点赞的图片列表，true表示公开
 * @property {boolean} showFavoriteContentList - 是否公开收藏的帖子列表，true表示公开
 * @property {boolean} showLikeContentList - 是否公开点赞的帖子列表，true表示公开
 * @property {boolean} showMyPhotoList - 是否公开我发布的图片列表，true表示公开
 * @property {boolean} showMyContentList - 是否公开我发布的帖子列表，true表示公开
 */
interface UserSettings {
  showFollowList: boolean;
  showFansList: boolean;
  showFavoritePhotoList: boolean;
  showLikePhotoList: boolean;
  showFavoriteContentList: boolean;
  showLikeContentList: boolean;
  showMyPhotoList: boolean;
  showMyContentList: boolean;
}

/**
 * 用户列表排序字段
 * - 'nickname': 按昵称字母顺序排序
 * - 'createTime': 按账号创建时间排序，最近创建的在前
 * - 'updateTime': 按账号更新时间排序，最近更新的在前
 * - 'visitedCount': 按主页访问量排序，访问量高的在前
 */
type UserSortField =
  | 'createTime'
  | 'nickname'
  | 'uid'
  | 'updateTime'
  | 'followingsCount'
  | 'visitedCount'
  | 'totalDownloads';

/**
 * 用户列表查询参数
 * @extends QueryTemplate<UserSortField>
 * @property {string} [keyword] - 搜索关键词，匹配昵称或简介
 * @property {'male' | 'female' | 'hidden'} [gender] - 按性别筛选
 * @property {string} [startDate] - 开始日期(YYYY-MM-DD)，用于筛选创建时间范围
 * @property {string} [endDate] - 结束日期(YYYY-MM-DD)，用于筛选创建时间范围
 */
type UserListQuery = QueryTemplate<UserSortField>;

/**
 * 修改密码请求参数
 * @property {string} oldPassword - 旧密码，6-20个字符，至少包含字母和数字
 * @property {string} newPassword - 新密码，6-20个字符，至少包含字母和数字
 * @property {string} confirmNewPassword - 确认新密码，必须与新密码一致
 */
interface changePasswordBody {
  oldPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

/**
 * 修改邮箱请求参数
 * @property {string} oldEmail - 旧邮箱地址，必须与当前绑定邮箱一致
 * @property {string} email - 新邮箱地址，必须为有效的邮箱格式
 * @property {string} code - 邮箱验证码，6位数字
 */
interface changeEmailBody {
  oldEmail: string;
  email: string;
  code: string;
}

/**
 * 修改手机号请求参数
 * @property {string} oldPhone - 旧手机号，必须与当前绑定手机号一致
 * @property {string} phone - 新手机号，11位数字
 * @property {string} code - 短信验证码，6位数字
 */
interface changePhoneBody {
  oldPhone: string;
  phone: string;
  code: string;
}

/**
 * 用户列表项
 * @property {string} uid - 用户唯一标识
 * @property {string} avatar - 用户头像URL
 * @property {string} background - 用户背景图URL
 * @property {string} nickname - 用户昵称
 * @property {boolean} isFollowing - 当前用户是否已关注该用户
 * @property {boolean} isFans - 该用户是否是当前用户的粉丝
 * @property {string} bio - 用户个人简介摘要
 * @property {string} lastLoginTime - 最后登录时间
 */
interface UsersList {
  uid: string;
  avatar: string;
  background: string;
  nickname: string;
  isFollowing: boolean;
  isFans: boolean;
  bio: string;
  lastLoginTime: string;
}
