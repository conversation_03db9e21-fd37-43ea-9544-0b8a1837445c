export const useMessageStore = defineStore('message', () => {
  const messageStore: Ref<Message[]> = ref([]);
  const delay = ref(1000);
  const dataUpdata = ref(0);
  const setMessage = (message: setMessage) => {
    messageStore.value = [
      {
        name: message.name || '消息',
        description: message.description || '',
        time: message.time || '刚刚',
        icon: (() => {
          switch (message.type) {
            case 'error':
              return '❌';
            case 'warning':
              return '⚠️';
            case 'success':
              return '✅';
            case 'info':
              return 'ℹ️';
            default:
              return '💬';
          }
        })(),
        color: (() => {
          switch (message.type) {
            case 'error':
              return 'red';
            case 'warning':
              return 'yellow';
            case 'success':
              return 'green';
            default:
              return 'blue';
          }
        })(),
      },
    ];
    dataUpdata.value = Math.random();
  };
  const getMessage = () => messageStore;
  const getDelay = () => delay.value;
  const getDataUpdata = () => dataUpdata;

  return {
    messageStore,
    getDataUpdata,
    getDelay,
    setMessage,
    getMessage,
  };
});
