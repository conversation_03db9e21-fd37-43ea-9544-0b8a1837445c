<template>
  <div class="rikka-select-container" :class="{ open: isOpen }">
    <div class="rune-decoration">✪</div>
    <div class="select-field" @click="toggleDropdown">
      <span class="selected-value">
        {{ selectedOption ? selectedOption.label : placeholder }}
      </span>
      <span class="arrow">
        <IconSvg name="bottomArrows" size="1.5rem" color="#fff" />
      </span>
    </div>

    <transition name="dropdown">
      <div v-if="isOpen" class="dropdown-overlay" @click="closeDropdown">
        <div class="dropdown-options" @click.stop>
          <div
            v-for="(option, index) in options"
            :key="index"
            class="option"
            :class="{
              selected: modelValue === option.value,
              disabled: option.disabled,
            }"
            @click="!option.disabled && selectOption(option)"
          >
            <span class="option-icon" v-if="option.icon">{{
              option.icon
            }}</span>
            <span class="option-label">{{ option.label }}</span>
            <span class="option-rune" v-if="modelValue === option.value"
              >✧</span
            >
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script lang="ts" setup>
// 定义组件props
const props = defineProps({
  // 双向绑定的值
  modelValue: {
    type: [String, Number],
    default: '',
  },
  // 下拉选项数组
  options: {
    type: Array as () => Array<{
      value: string | number; // 选项值
      label: string; // 显示文本
      icon?: string; // 可选图标
      disabled?: boolean; // 是否禁用
    }>,
    default: () => [],
  },
  // 未选择时的占位文本
  placeholder: {
    type: String,
    default: '',
  },
});

// 定义组件事件
const emit = defineEmits(['update:modelValue']);

// 控制下拉框展开状态
const isOpen = ref(false);
// 计算当前选中的选项
const selectedOption = computed(() => {
  return props.options.find((opt) => opt.value === props.modelValue) || null;
});

// 切换下拉框展开/收起
const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
};

// 收起下拉框
const closeDropdown = () => {
  isOpen.value = false;
};

// 选择选项
const selectOption = (option: { value: string | number; label: string }) => {
  emit('update:modelValue', option.value); // 触发更新事件
  isOpen.value = false; // 选择后自动关闭下拉框
};

// 点击外部关闭下拉框
const closeOnClickOutside = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (!target.closest('.rikka-select-container')) {
    isOpen.value = false;
  }
};

// 组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener('click', closeOnClickOutside);
});

// 组件卸载前移除事件监听
onBeforeUnmount(() => {
  document.removeEventListener('click', closeOnClickOutside);
});
</script>

<style lang="scss">
.rikka-select-container {
  position: relative;

  .rune-decoration {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: -0.7rem;
    color: var(--select-rune-color);
    font-size: 1.8rem;
    opacity: 0.7;
    text-shadow: var(--select-rune-glow);
    transition: all 0.3s ease;
    animation: glow 3s infinite;
    z-index: 1;
  }

  .select-field {
    position: relative;
    width: 100%;
    padding: 0.7rem 1rem;
    background: var(--select-field-bg);
    border: var(--select-field-border);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: var(--select-field-shadow);
    cursor: pointer;
    backdrop-filter: blur(0.125rem);
    text-shadow: 0 0 0.1875rem rgba(216, 193, 255, 0.7);

    .selected-value {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .arrow {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
    }
  }

  &:hover .rune-decoration {
    opacity: 1;
    text-shadow: 0 0 0.9375rem rgba(var(--magic-pink-rgb), 0.8);
  }

  &.open {
    .rune-decoration {
      color: var(--magic-pink);
      text-shadow: 0 0 0.9375rem rgba(var(--magic-pink-rgb), 0.8);
      opacity: 1;
    }

    .select-field {
      border: var(--select-open-border);
      box-shadow:
        0 0 1.25rem rgba(var(--interactive-primary-rgb), 0.5),
        inset 0 0 0.625rem rgba(var(--interactive-primary-rgb), 0.3);
      background: var(--select-open-bg);
    }
  }

  .dropdown-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--background-overlay);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    backdrop-filter: blur(0.1875rem);
    padding-top: 5rem;
    z-index: 10;
  }

  .dropdown-options {
    position: relative;
    width: 100%;
    max-width: 31.25rem;
    background: var(--select-dropdown-bg);
    border: var(--select-dropdown-border);
    border-radius: var(--border-radius-md);
    box-shadow:
      0 1.25rem 2.5rem rgba(0, 0, 0, 0.6),
      inset 0 0 1.25rem rgba(0, 0, 0, 0.3);
    max-height: 60vh;
    overflow-y: auto;
    padding: 1rem;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.25rem;
      background: var(--select-dropdown-header);
    }
  }

  .option {
    padding: 0.8rem 1.2rem;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    position: relative;
    border-radius: var(--border-radius-sm);
    margin-bottom: 0.5rem;
    background: var(--select-option-bg);
    border: 0.0625rem solid #434343;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: var(--select-option-hover-bg);
      box-shadow: var(--shadow-dimension-sm);
      text-shadow: 0 0 0.5rem rgba(216, 193, 255, 0.8);

      .option-icon {
        text-shadow: 0 0 0.5rem rgba(var(--magic-pink-rgb), 0.8);
      }
    }

    &.selected {
      background: var(--select-option-selected-bg);
      color: var(--text-primary);
      font-weight: bold;
      box-shadow: var(--shadow-neon-primary);

      .option-rune {
        opacity: 1;
      }
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      background: rgba(70, 20, 120, 0.1);
    }

    .option-icon {
      margin-right: 0.75rem;
      transition: all 0.2s;
      font-size: 1.3rem;
      color: var(--magic-pink);
      text-shadow: 0 0 0.3125rem rgba(var(--magic-pink-rgb), 0.5);
      min-width: 1.5rem;
      text-align: center;
    }

    .option-label {
      flex-grow: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .option-rune {
      position: absolute;
      right: 1rem;
      color: var(--magic-pink);
      text-shadow: 0 0 0.5rem rgba(var(--magic-pink-rgb), 0.8);
      opacity: 0;
      transition: opacity 0.3s;
      font-size: 1.2rem;
    }
  }
}

/* 下拉框动画 */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: opacity 0.3s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
}

.dropdown-enter-active .dropdown-options,
.dropdown-leave-active .dropdown-options {
  transition:
    transform 0.4s cubic-bezier(0.34, 1.56, 0.64, 1),
    opacity 0.4s ease;
}

.dropdown-enter-from .dropdown-options,
.dropdown-leave-to .dropdown-options {
  opacity: 0;
  transform: translateY(-20px) scale(0.95);
}

@keyframes glow {
  0% {
    text-shadow: 0 0 5px rgba(183, 0, 255, 0.5);
  }
  50% {
    text-shadow: 0 0 15px rgba(183, 0, 255, 0.8);
  }
  100% {
    text-shadow: 0 0 5px rgba(183, 0, 255, 0.5);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes float {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}
</style>

