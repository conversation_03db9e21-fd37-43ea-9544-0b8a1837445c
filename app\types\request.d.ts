/**
 * 后端API响应结构泛型
 * @template T - 响应数据类型，默认为unknown
 */
interface ApiResponse<T = unknown> {
  /** 状态码，1表示成功，0表示错误 */
  code: number;
  /** 响应数据内容 */
  data: T;
  /** 可选错误或提示消息 */
  message?: string;
}

/**
 * 通用查询参数模板
 * @template T - 排序字段类型，默认为string
 */
interface QueryTemplate<T = string> {
  /** 当前页码，从1开始 */
  page?: number;
  /** 每页显示记录数 */
  pageSize?: number;
  /** 排序字段名称 */
  sortField?: T;
  /** 排序方向：asc(升序)或desc(降序) */
  sortOrder?: 'asc' | 'desc';
  /** 搜索关键词 */
  keyword?: string;
}

/**
 * 分页列表响应模板
 * @template T - 列表项数据类型
 * @template F - 排序字段类型，默认为string
 * @template O - 排序方向类型，默认为'asc'|'desc'
 */
interface GetListTemplate<T = any, F = string, O = 'asc' | 'desc'> {
  /** 当前页码 */
  page: number;
  /** 每页记录数 */
  pageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
  /** 当前排序字段 */
  sortField: F;
  /** 当前排序方向 */
  sortOrder: O;
  /** 当前页数据列表 */
  list: T[];
}

/**
 * 分页信息模板
 * @template T - 排序字段类型，默认为string
 * @template O - 排序方向类型，默认为'asc'|'desc'
 */
interface Paginated<T = string, O = 'asc' | 'desc'> {
  /** 当前页码 */
  page: number;
  /** 每页记录数 */
  pageSize: number;
  /** 总记录数 */
  totalCount: number;
  /** 总页数 */
  totalPage: number;
  /** 当前排序字段 */
  sortField: T;
  /** 当前排序方向 */
  sortOrder: O;
}
