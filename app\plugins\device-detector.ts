/**
 * 设备检测插件
 * 用于识别客户端设备类型（移动设备/平板/桌面）
 * 支持服务端渲染(SSR)和客户端渲染(CSR)两种场景
 */

// 导入UA解析库，用于解析浏览器userAgent字符串
import { UAParser } from 'ua-parser-js';

/**
 * 定义Nuxt插件
 * @param nuxtApp - Nuxt应用实例
 */
export default defineNuxtPlugin((nuxtApp) => {
  // 从路由状态存储中获取设置设备类型的方法
  const { setDevice } = useRouterStore();

  /**
   * 用户代理字符串
   * @type {string}
   */
  let userAgent = '';

  // 服务端渲染逻辑
  if (import.meta.server) {
    /**
     * 从SSR上下文中获取请求头中的user-agent
     * 使用可选链操作符安全访问可能不存在的属性
     */
    userAgent = nuxtApp.ssrContext?.event.req.headers['user-agent'] || '';
  }
  // 客户端渲染逻辑
  else {
    /**
     * 直接从浏览器navigator对象获取userAgent
     */
    userAgent = navigator.userAgent;
  }

  /**
   * 创建UA解析器实例
   * @type {UAParser}
   */
  const parser = new UAParser(userAgent);

  /**
   * 解析设备信息
   * @type {UAParser.IDevice}
   */
  const device = parser.getDevice();

  /**
   * 格式化后的设备信息对象
   * @type {object}
   * @property {boolean} isMobile - 是否为移动设备
   * @property {boolean} isTablet - 是否为平板设备
   * @property {boolean} isDesktop - 是否为桌面设备
   * @property {DeviceType} type - 设备类型
   */
  const deviceInfo = {
    // 判断是否为移动设备
    isMobile: device.type === 'mobile',
    // 判断是否为平板设备
    isTablet: device.type === 'tablet',
    // 判断是否为桌面设备（包括未识别设备类型的情况）
    isDesktop: !device.type || device.type === undefined,
    // 设备类型，未识别则默认为桌面
    type: device.type || 'desktop',
  } as {
    isMobile: boolean;
    isTablet: boolean;
    isDesktop: boolean;
    type: DeviceType;
  };

  // 更新路由状态中的设备类型
  setDevice(deviceInfo.type);

  /**
   * 插件返回值
   * 通过provide将设备信息注入Nuxt应用
   */
  return {
    provide: {
      device: deviceInfo,
    },
  };
});

