<template>
  <div class="home-layout">
    <AppHearder>
      <template #left>
        <div class="logo">
          <Motion
            :hover="{ scale: 0.9 }"
            :press="{
              scale: 0.8,
            }"
          >
            <img src="/logo.png" alt="" />
          </Motion>
        </div>
      </template>
    </AppHearder>
    <main class="main">
      <div class="left">
        <AppSideMenu :navItems="navItems"></AppSideMenu>
      </div>
      <div class="right">
        <slot />
      </div>
    </main>
    <AppFooter />
  </div>
</template>

<script lang="ts" setup>
import type { NavItem } from '~/components/AppSideMenu.vue';

// 导航条数据
const navItems: NavItem[] = [
  {
    name: '推荐',
    path: '/',
    icon: 'recommend',
  },
  {
    name: '图片',
    path: '/photos',
    icon: 'image',
  },
  {
    name: '帖子',
    path: '/posts',
    icon: 'posts',
  },
  {
    name: '关注',
    path: '/follow',
    icon: 'follow',
  },
  {
    name: '社区',
    path: '/community',
    icon: 'community',
  },
  {
    name: '我的',
    path: '/mine',
    icon: 'mine',
  },
];
</script>

<style lang="scss" scoped>
/* 整个页面的容器样式 */
.home-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-base);
  color: var(--text-primary);

  .logo {
    flex: 1;
    display: flex;

    img {
      width: 10rem;
    }
  }

  /* 主要内容区域样式 */
  > .main {
    flex: 1;
    display: flex;
    margin-right: 0.5rem;
    min-height: 0; /* 修复flex容器滚动问题 */

    > .left {
      padding: 1rem 1rem;
    }

    > .right {
      flex: 1;
      background: var(--background-elevated);
      border-radius: 1rem;
      overflow: hidden;
      display: flex; /* 新增flex容器 */
      flex-direction: column; /* 垂直排列 */

      /* 滚动区域 */
      > * {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch; /* 平滑滚动 */
      }
    }
  }
}
</style>
