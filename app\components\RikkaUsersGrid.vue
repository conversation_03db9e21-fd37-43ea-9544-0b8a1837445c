<template>
  <div
    class="user-grid-container"
    :style="{ '--grid-gap': gap + 'px' }"
    ref="scrollContainer"
  >
    <div class="user-grid" ref="gridContainer">
      <div
        v-for="(col, colIndex) in waterfallColumns"
        :key="`col-${colIndex}`"
        class="user-column"
      >
        <slot
          name="item"
          :items="col"
          :cardWidth="actualCardWidth"
          :cardHeight="actualCardHeight"
        >
          <AppUserCard
            v-for="item in col"
            :key="item.uid"
            :users="item"
            :fixedWidth="actualCardWidth"
            :fixedHeight="actualCardHeight"
            @toggle-follow="$emit('toggle-follow', $event)"
          />
        </slot>
      </div>
    </div>

    <!-- 预加载哨兵 -->
    <div
      v-if="!rowLimited"
      class="preload-sentinel"
      ref="preloadSentinel"
      :style="{
        height: preloadDistance + 'px',
        position: 'absolute',
        width: '1px',
        pointerEvents: 'none',
        top: 'calc(100% - ' + preloadDistance + 'px)',
        left: 0,
        zIndex: -1,
      }"
    ></div>

    <!-- 加载状态 -->
    <div class="bottom-status" ref="bottomStatus">
      <div
        v-if="(!rowLimited || maxRows === 0) && loading"
        class="loading-slot"
      >
        <slot name="loading">
          <div class="default-loading">
            <div class="spinner"></div>
            <p>努力加载中....</p>
          </div>
        </slot>
      </div>

      <div
        v-else-if="(!rowLimited || maxRows === 0) && !hasMore"
        class="end-slot"
      >
        <slot name="end">
          <div class="default-end">
            <p>已经到底啦！</p>
          </div>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from 'vue';

// 组件属性定义
const props = defineProps({
  users: {
    type: Array as PropType<UsersList[]>,
    required: true,
    default: () => [],
  },
  columns: {
    type: Number,
    default: 0,
  },
  cardWidth: {
    type: Number,
    default: 280,
  },
  cardHeight: {
    type: Number,
    default: 420,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  hasMore: {
    type: Boolean,
    default: true,
  },
  gap: {
    type: Number,
    default: 20,
  },
  preloadDistance: {
    type: Number,
    default: 300,
  },
  maxRows: {
    type: Number,
    default: 0,
  },
});

// 组件事件定义
const emit = defineEmits(['layout-updated', 'load-more', 'toggle-follow']);

// 响应式变量
const gridContainer = ref<HTMLElement | null>(null);
const scrollContainer = ref<HTMLElement | null>(null);
const preloadSentinel = ref<HTMLElement | null>(null);
const actualCardWidth = ref(props.cardWidth);
const actualCardHeight = ref(props.cardHeight);
const { width: containerWidth } = useElementSize(gridContainer);
const intersectionObserver = ref<IntersectionObserver | null>(null);
const currentMaxRows = ref(props.maxRows);
const rowLimited = ref(props.maxRows > 0);
const autoLoadAttempted = ref(false);
const waterfallColumns = ref<UsersList[][]>([]); // 瀑布流列数据
const columnHeights = ref<number[]>([]); // 列高度记录

// 添加窗口大小监听
const { width: windowWidth } = useWindowSize();

// SSR兼容标志
const isMounted = ref(false);
if (typeof window === 'undefined') {
  isMounted.value = false;
}
onMounted(() => {
  isMounted.value = true;
  updateGridLayout();
  initIntersectionObserver();

  // 挂载后立即检查是否需要自动加载
  nextTick(checkAutoLoad);
});

// 计算实际列数
const actualColumns = computed(() => {
  if (props.columns > 0) return props.columns;
  if (!isMounted.value) return 3;

  // 计算可用宽度
  const totalWidth = containerWidth.value || windowWidth.value;
  if (!totalWidth) return 3;

  // 减去容器的左右padding (1rem = 16px)
  const containerPadding = 32; // 1rem * 2 (左右)
  const availableWidth = totalWidth - containerPadding;
  if (availableWidth <= 0) return 1;

  // 计算最小卡片宽度（防止过度压缩）
  const minCardWidth = Math.max(120, props.cardWidth);

  // 考虑间距的列数计算
  // 公式：availableWidth = columns * cardWidth + (columns - 1) * gap
  // 解得：columns = (availableWidth + gap) / (cardWidth + gap)
  const maxColumns = Math.floor(
    (availableWidth + props.gap) / (minCardWidth + props.gap)
  );
  return Math.max(1, maxColumns);
});

// 计算卡片尺寸
const calculateCardSize = () => {
  if (!isMounted.value) return;

  // 优先使用容器宽度
  const totalWidth = containerWidth.value || windowWidth.value;
  if (!totalWidth) {
    actualCardWidth.value = props.cardWidth;
    actualCardHeight.value = props.cardHeight;
    return;
  }

  // 减去容器的左右padding (1rem = 16px)
  const containerPadding = 32; // 1rem * 2 (左右)
  const availableWidth = totalWidth - containerPadding;

  // 计算总间隙宽度
  const totalGap = (actualColumns.value - 1) * props.gap;

  // 计算卡片宽度
  actualCardWidth.value = Math.max(
    Math.max(120, props.cardWidth),
    (availableWidth - totalGap) / actualColumns.value
  );

  // 保持原始宽高比计算高度
  actualCardHeight.value = props.cardHeight;
};

// 更新网格布局（防抖）
const updateGridLayout = useDebounceFn(() => {
  calculateCardSize();
  calculateWaterfallLayout();
  emit('layout-updated');
}, 100);

// 瀑布流布局计算
const calculateWaterfallLayout = () => {
  if (!isMounted.value || !props.users.length) {
    waterfallColumns.value = [];
    return;
  }

  const columnCount = actualColumns.value;

  // 初始化列数据
  const columns: UsersList[][] = Array.from({ length: columnCount }, () => []);
  columnHeights.value = Array(columnCount).fill(0);

  // 计算最大显示数量（行数限制）
  const maxItems = rowLimited.value
    ? Math.min(props.users.length, currentMaxRows.value * columnCount)
    : props.users.length;

  // 分配元素到最短列
  for (let i = 0; i < maxItems; i++) {
    const item = props.users[i];
    if (!item) continue;
    let shortestColumnIndex = 0;

    // 找到当前最短的列
    for (let j = 1; j < columnCount; j++) {
      if (columnHeights.value[j]! < columnHeights.value[shortestColumnIndex]!) {
        shortestColumnIndex = j;
      }
    }

    columns[shortestColumnIndex]!.push(item);

    // 更新列高度（固定高度 + 间距）
    columnHeights.value[shortestColumnIndex]! +=
      actualCardHeight.value + props.gap;
  }

  waterfallColumns.value = columns;
};

// 计算显示的用户（用于行数限制）
const displayedUsers = computed(() => {
  if (!rowLimited.value) return props.users;
  if (!isMounted.value) return props.users;

  const maxItems = Math.max(
    actualColumns.value,
    currentMaxRows.value * actualColumns.value
  );
  return props.users.slice(0, maxItems);
});

// 自动补齐检查
const checkAutoLoad = () => {
  if (
    !isMounted.value ||
    props.loading ||
    !props.hasMore ||
    autoLoadAttempted.value
  )
    return;

  // 计算实际需要的元素数量（一行）
  const requiredItems = actualColumns.value;

  // 如果当前元素不足一行且还有更多数据
  if (displayedUsers.value.length < requiredItems) {
    autoLoadAttempted.value = true;
    emit('load-more');
  }
};

// 滚动事件处理
const handleScroll = useDebounceFn(() => {
  if (!scrollContainer.value || !isMounted.value) return;
  checkLoadMore();
}, 50);

// 加载更多检查
const checkLoadMore = () => {
  if (props.loading || !props.hasMore || !preloadSentinel.value) return;
  const container = scrollContainer.value;
  if (!container) return;

  const containerRect = container.getBoundingClientRect();
  const sentinelRect = preloadSentinel.value.getBoundingClientRect();
  const distanceToBottom = sentinelRect.top - containerRect.bottom;

  if (distanceToBottom <= props.preloadDistance) {
    emit('load-more');
  }
};

// 初始化交叉观察器
const initIntersectionObserver = () => {
  if (!isMounted.value || !preloadSentinel.value) return;

  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
  }

  intersectionObserver.value = new IntersectionObserver(
    (entries) => {
      if (entries[0]?.isIntersecting && !props.loading && props.hasMore) {
        emit('load-more');
      }
    },
    {
      root: scrollContainer.value,
      rootMargin: '0px',
      threshold: 0.01,
    }
  );

  if (preloadSentinel.value) {
    intersectionObserver.value.observe(preloadSentinel.value);
  }
};

// 监听相关数据变化
watch(
  () => [props.users, actualColumns.value, props.gap, props.maxRows],
  () => {
    if (!isMounted.value) return;
    updateGridLayout();

    // 重置自动补齐尝试标志
    if (props.users.length > 0) {
      autoLoadAttempted.value = false;
    }
  },
  { deep: true }
);

watch(containerWidth, () => {
  if (!isMounted.value) return;
  updateGridLayout();
});

watch(
  () => props.preloadDistance,
  () => initIntersectionObserver()
);

// 添加窗口大小变化监听
watch(windowWidth, () => {
  if (!isMounted.value) return;
  updateGridLayout();
});

// 生命周期钩子
onMounted(() => {
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('scroll', handleScroll);
  }
});

onUnmounted(() => {
  intersectionObserver.value?.disconnect();
  if (scrollContainer.value) {
    scrollContainer.value.removeEventListener('scroll', handleScroll);
  }
});
</script>

<style scoped lang="scss">
.user-grid-container {
  --grid-gap: 20px;
  --card-radius: 12px;
  --card-shadow: 0 10px 20px rgba(75, 0, 130, 0.15);
  --loading-color: #ffd700;
  --text-color: #e6e6fa;
  --spinner-size: 40px;

  width: 100%;
  position: relative;
  padding: 1rem;

  .user-grid {
    display: flex;
    gap: var(--grid-gap);

    .user-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--grid-gap);
    }
  }

  .preload-sentinel {
    position: absolute;
    width: 1px;
    pointer-events: none;
    top: calc(100% - v-bind('props.preloadDistance + "px"'));
    left: 0;
    z-index: -1;
  }

  .bottom-status {
    width: 100%;
    padding: 1rem 0;
    text-align: center;

    .loading-slot,
    .end-slot {
      .default-loading,
      .default-end {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 0.4rem;
        color: var(--text-color);
        font-size: 0.9rem;

        .spinner {
          width: var(--spinner-size);
          height: var(--spinner-size);
          border: 3px solid rgba(255, 215, 0, 0.2);
          border-radius: 50%;
          border-top-color: var(--loading-color);
          animation: spin 1s ease-in-out infinite;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            box-shadow: 0 0 15px #ffd700;
            animation: glow 1.5s ease-in-out infinite;
          }
        }
      }
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes glow {
  0%,
  100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
</style>
