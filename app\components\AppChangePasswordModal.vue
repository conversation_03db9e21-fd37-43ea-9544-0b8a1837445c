<template>
  <RikkaDialog
    :show="show"
    title="修改密码"
    :width="dialogWidth"
    :mask-closable="false"
    @close="close"
  >
    <!-- 表单主体区域 -->
    <div class="change-password-modal">
      <!-- 旧密码输入区域 -->
      <div class="lab">
        <span> 旧密码: </span>
        <RikkaInput
          v-model="data.oldPassword"
          :password="true"
          placeholder="请输入旧密码"
          @keyup.enter="handleConfirm"
        />
      </div>

      <!-- 新密码输入区域 -->
      <div class="lab">
        <span> 新密码: </span>
        <RikkaInput
          v-model="data.newPassword"
          :password="true"
          placeholder="请输入新密码"
          @keyup.enter="handleConfirm"
        />
      </div>

      <!-- 密码确认区域 -->
      <div class="lab">
        <span> 再确认: </span>
        <RikkaInput
          v-model="data.confirmNewPassword"
          :password="true"
          placeholder="请确认新密码"
          @keyup.enter="handleConfirm"
        />
      </div>
    </div>

    <!-- 对话框底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <RippleButton class="btn-cancel" @click="close()">取消</RippleButton>
        <RippleButton
          class="btn-confirm"
          @click="handleConfirm"
          :disabled="isDisabled"
          >确定</RippleButton
        >
      </div></template
    >
  </RikkaDialog>
</template>

<script lang="ts" setup>
// 组件属性定义
const { show } = defineProps({
  show: Boolean, // 控制模态框显示状态
});

// 认证状态管理
const authStore = useAuthStore();

// 移动端适配 - 动态计算对话框宽度
const windowWidth = ref(0);
const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '90vw' : '30rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});

// 事件发射器定义
const emit = defineEmits<{
  close: [value: boolean]; // 关闭事件，携带是否成功标志
}>();

// 关闭处理函数（含表单重置）
const close = (flag = false) => {
  data.value = {
    oldPassword: '',
    newPassword: '',
    confirmNewPassword: '',
  };
  emit('close', flag);
};

// 表单数据模型
const data = ref({
  oldPassword: '', // 旧密码
  newPassword: '', // 新密码
  confirmNewPassword: '', // 确认密码
});

// 确认按钮禁用状态计算
const isDisabled = computed(() => {
  return (
    !authStore.getIsLogin() ||
    !data.value.newPassword ||
    !data.value.confirmNewPassword ||
    !data.value.oldPassword
  );
});

// 确定修改密码
const handleConfirm = async () => {
  if (isDisabled.value) return;

  if (data.value.newPassword !== data.value.confirmNewPassword) {
    return useMessage({
      name: '请重新输入',
      description: '两次输入的新密码不一致',
      type: 'error',
    });
  }
  if (!checkPassword(data.value.newPassword)) {
    return useMessage({
      name: '新密码格式错误',
      description: '密码长度为6-18位，大小写字母、数字和特殊字符组合',
      type: 'error',
    });
  }

  try {
    await useApi().changePassword(data.value);
    useMessage({
      name: '修改成功',
      description: '修改成功，请重新登录',
      type: 'success',
    });
    await useApi().logout();
    close(true);
    return;
  } catch (err) {}
};
</script>

<style lang="scss" scoped>
.lab {
  display: flex;
  align-items: center;
  margin-bottom: 0.3rem;

  > span {
    margin-right: 1rem;
  }

  > div {
    flex: 1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-cancel-hover);
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

