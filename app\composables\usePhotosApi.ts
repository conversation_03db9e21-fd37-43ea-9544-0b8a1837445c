/**
 * 图片相关API组合式函数
 * 提供图片上传、发布、管理等功能接口
 */

/**
 * 上传图片到服务器
 * @param file - 要上传的图片文件对象
 * @returns 返回包含图片URL、下载文件名和属性的数组
 */
const uploadPhoto = async (file: File) => {
  try {
    // 创建FormData对象用于文件上传
    const formData = new FormData();
    formData.append('file', file); // 添加文件到表单数据

    // 发送上传请求到图片上传接口
    const data = await useRequest<
      ApiResponse<
        { url: string; downloadFilename: string; attributes: Attribute }[]
      >
    >('https://file.sixflower.top/upload/image', {
      method: 'POST',
      body: formData,
    });

    return data.data; // 返回上传结果数据
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 发布图片信息到系统
 * @param body - 包含图片发布信息的对象
 * @returns 返回发布结果数据
 */
const publishPhoto = async (body: photoUploadInfo) => {
  try {
    // 发送发布请求到图片发布接口
    const { data } = await useRequest<ApiResponse<GetSelfPhotosInfo>>(
      '/photo',
      {
        method: 'POST',
        body,
      }
    );

    return data; // 返回发布结果
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 获取用户自己发布的图片信息
 * @param id - 图片ID
 * @returns 返回图片详细信息
 */
const getMyPhotosInfo = async (id: string) => {
  try {
    // 发送获取请求到图片详情接口
    const { data } = await useRequest<ApiResponse<GetSelfPhotosInfo>>(
      '/photo/' + id
    );
    return data; // 返回图片信息
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 更新用户自己发布的图片信息
 * @param id - 要更新的图片ID
 * @param body - 包含更新信息的对象
 * @returns 返回更新结果
 */
const updateMyPhoto = async (id: string, body: any) => {
  try {
    // 发送更新请求到图片更新接口
    const { data } = await useRequest<ApiResponse<GetSelfPhotosInfo>>(
      '/photo/' + id,
      {
        method: 'PUT',
        body,
      }
    );
    return data; // 返回更新结果
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 删除用户自己发布的图片
 * @param id - 要删除的图片ID
 * @returns 返回删除结果
 */
const deleteMyPhoto = async (id: string) => {
  try {
    // 发送删除请求到图片删除接口
    const { data } = await useRequest('/photo/' + id, {
      method: 'DELETE',
    });
    return data; // 返回删除结果
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 获取其他用户上传的图片详情
 * @param id - 图片ID
 * @returns 返回图片详细信息
 */
const getOtherPhotoDetail = async (id: string) => {
  const authStore = useAuthStore(); // 获取认证状态
  try {
    // 发送获取请求到公开图片详情接口
    const { data } = await useRequest<ApiResponse<GetOtherPhotosInfo>>(
      '/photo/public/' + id,
      {
        token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
      }
    );
    return data; // 返回图片信息
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 获取图片列表
 * @param query - 可选查询参数
 * @returns 返回分页图片列表
 */
const getPhotosList = async (query?: PhotosListQuery) => {
  const authStore = useAuthStore(); // 获取认证状态
  try {
    // 发送获取请求到公开图片列表接口
    const { data } = await useRequest<
      ApiResponse<GetListTemplate<PhotosList, PhotosSortField>>
    >('/photo/public/all', {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
    });
    return data; // 返回图片列表
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 获取指定用户发布的图片列表
 * @param uid - 用户ID
 * @returns 返回该用户的图片列表
 */
const getUserPhotosList = async (uid: string, query?: PhotosListQuery) => {
  const authStore = useAuthStore(); // 获取认证状态
  try {
    // 发送获取请求到用户图片列表接口
    const { data } = await useRequest<
      ApiResponse<GetListTemplate<GetSelfPhotosInfo, PhotosSortField>>
    >('/photo/public/all/' + uid, {
      query: {
        sortField: 'createTime', // 默认按创建时间排序
        sortOrder: 'desc', // 默认降序
        ...query, // 合并传入的查询参数
      },
      token: authStore.isAuthenticated, // 根据认证状态决定是否携带token
    });
    return data; // 返回图片列表
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 从服务器下载图片
 * @param id - 图片ID
 * @returns 返回图片的Blob数据
 */
const downloadPhoto = async (id: string) => {
  try {
    // 发送获取请求到图片下载接口
    const { data: downName } = await useRequest<ApiResponse<string>>(
      '/photo/public/download/' + id
    );
    // 发送下载请求到图片下载接口
    const blob = await useRequest<Blob>(
      'https://file.sixflower.top/download/' + downName,
      {
        method: 'GET',
        token: false, // 不需要认证token
        server: false, // 不通过服务端代理
      }
    );
    return blob; // 返回图片Blob数据
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 切换图片点赞状态
 * @param id - 图片ID
 * @returns 无返回值
 */
const toggleLikePhoto = async (id: string) => {
  try {
    // 发送点赞/取消点赞请求
    const { data } = await useRequest('/photo/like/' + id, {
      method: 'POST',
    });
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 切换图片收藏状态
 * @param id - 图片ID
 * @returns 无返回值
 */
const toggleFavoritePhoto = async (id: string) => {
  try {
    // 发送收藏/取消收藏请求
    const { data } = await useRequest('/photo/favorite/' + id, {
      method: 'POST',
    });
  } catch (err) {
    throw err; // 抛出错误
  }
};

/**
 * 导出图片相关API
 * @returns 包含所有图片API方法的对象
 */
export const usePhotosApi = () => {
  return {
    /** 上传图片方法 */
    uploadPhoto,
    /** 发布图片方法(添加节流500ms) */
    publishPhoto: useThrottleFn(publishPhoto, 500),
    /** 获取自己图片信息方法(添加节流500ms) */
    getMyPhotosInfo: useThrottleFn(getMyPhotosInfo, 500),
    /** 更新图片信息方法(添加节流500ms) */
    updateMyPhoto: useThrottleFn(updateMyPhoto, 500),
    /** 删除图片方法(添加节流500ms) */
    deleteMyPhoto: useThrottleFn(deleteMyPhoto, 500),
    /** 获取图片列表方法(添加节流500ms) */
    getPhotosList: useThrottleFn(getPhotosList, 500),
    /** 获取图片详情方法(添加节流500ms) */
    getOtherPhotoDetail: useThrottleFn(getOtherPhotoDetail, 500),
    /** 获取其他用户上传的图片列表方法(添加节流500ms) */
    getUserPhotosList: useThrottleFn(getUserPhotosList, 500),
    /** 切换图片点赞状态方法(添加节流500ms) */
    toggleLikePhoto: useThrottleFn(toggleLikePhoto, 500),
    /** 切换图片收藏状态方法(添加节流500ms) */
    toggleFavoritePhoto: useThrottleFn(toggleFavoritePhoto, 500),
    /** 从服务器下载图片方法(添加节流500ms) */
    downloadPhoto: useThrottleFn(downloadPhoto, 500),
  };
};

