// 导入dayjs日期处理库
import dayjs from 'dayjs';

/**
 * 计算年龄工具函数
 * @param {string | undefined} birthday - 生日日期字符串，格式应为dayjs可解析的格式(如YYYY-MM-DD)
 * @returns {number | string} - 返回计算出的年龄(数字)或'-'(当生日无效时)
 *
 * 功能说明：
 * 1. 根据输入的生日日期计算当前年龄
 * 2. 处理无效日期情况，返回'-'作为占位符
 * 3. 使用dayjs进行精确的日期差计算
 *
 * 使用示例：
 * calculateAge('1990-05-15') => 33 (假设当前年份是2023)
 * calculateAge(undefined) => '-'
 */
export const calculateAge = (birthday: string | undefined) => {
  // 检查生日参数是否存在
  if (birthday) {
    // 使用dayjs解析生日日期
    const birthDate = dayjs(birthday);
    // 获取当前日期
    const currentDate = dayjs();
    // 计算两个日期之间的年份差
    return currentDate.diff(birthDate, 'year');
  }
  // 当生日无效时返回占位符
  return '-';
};

