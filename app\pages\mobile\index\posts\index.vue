<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-帖子</Title>
    </Head>
    <MobileDrawer />
    <RikkaPullToRefresh @refresh="handleRefresh">
      <!-- 帖子瀑布流画廊组件 -->
      <RikkaPostsWaterfallGallery
        :posts="postsArray"
        :columns="0"
        :card-width="Number($device.isMobile ? 140 : 300)"
        :gap="10"
        :loading="loading"
        :has-more="hasMore"
        @load-more="handleLoadMore"
      >
        <!-- 使用 MobilePostCard 组件 -->
        <template #item="{ items, columnWidth }">
          <MobilePostCard
            v-for="post in items"
            :key="post.id"
            :post="post"
            :fixed-width="columnWidth"
          />
        </template>
      </RikkaPostsWaterfallGallery>
    </RikkaPullToRefresh>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'mobile',
});

const postsArray = ref<PostsWaterfallGalleryItem[]>([]); // 帖子数组
const loading = ref(false); // 加载状态
const hasMore = computed(
  () => paginated.value.page < paginated.value.totalPage
); // 是否有更多
const paginated = ref<Paginated>({
  page: 0,
  pageSize: 0,
  totalCount: 0,
  totalPage: 1,
  sortField: '',
  sortOrder: 'desc',
});

/** 获取帖子列表 */
const getPostsList = async (query?: PostsListQuery) => {
  try {
    const { list, ...res } = await useApi().getPostList({
      ...query,
    });

    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos, // 保留原有 photos 字段
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    postsArray.value.push(...newData);
    paginated.value = res;
  } catch (err) {
    throw err;
  }
};

/** 刷新帖子列表 */
const refreshPostsList = async () => {
  try {
    const { list, ...res } = await useApi().getPostList({
      page: 1,
      pageSize: 20,
    });

    // 处理每条帖子的关联图片信息
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;

        return {
          ...rest,
          photos, // 保留原有 photos 字段
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );

    // 刷新时清空原有数据，重新加载
    postsArray.value = newData;
    paginated.value = res;

    // 显示刷新成功提示
    useMessage({
      name: '刷新成功',
      description: `帖子列表已刷新`,
      type: 'success',
    });
  } catch (err) {
    // 显示刷新失败提示
    useMessage({
      name: '刷新失败',
      description: '网络连接异常，请稍后重试',
      type: 'error',
    });
    // 不再抛出异常，避免在handleRefresh中重复处理
    console.error('刷新帖子列表失败:', err);
  }
};

/** 下拉刷新处理 */
const handleRefresh = async (done: () => void) => {
  try {
    await refreshPostsList();
  } catch (err) {
    console.error('刷新失败:', err);
  } finally {
    // 完成刷新
    done();
  }
};

/** 加载更多帖子 */
const handleLoadMore = async () => {
  if (loading.value || !hasMore.value) {
    return;
  }
  loading.value = true;
  await getPostsList({ page: paginated.value.page + 1, pageSize: 20 });
  loading.value = false;
};

/** 初始化数据加载 */
const initializeData = async () => {
  loading.value = true;
  try {
    await getPostsList({ page: 1, pageSize: 20 });
  } catch (err) {
    console.error('初始化数据加载失败:', err);
  } finally {
    loading.value = false;
  }
};

// 页面挂载时加载初始数据
onMounted(() => {
  initializeData();
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;

  display: flex;
  flex-direction: column;

  > :last-child {
    flex: 1;
    overflow: auto;
  }
}
</style>

