<template>
  <div class="rikka-uploader">
    <div class="upload-container">
      <!-- 上传区域 -->
      <div
        class="upload-area"
        :class="{
          'is-dragover': isDragover,
          'has-preview': !multiple && previewList.length,
        }"
        @dragenter.prevent="handleDragEnter"
        @dragover.prevent="handleDragOver"
        @dragleave.prevent="handleDragLeave"
        @drop.prevent="handleDrop"
        @click="triggerFileInput"
      >
        <input
          ref="fileInput"
          type="file"
          accept="image/*"
          class="file-input"
          @change="handleFileChange"
          :multiple="multiple"
        />

        <!-- 单图模式预览 -->
        <div v-if="!multiple && previewList.length" class="single-preview">
          <img :src="previewList[0]?.url" alt="预览图" class="preview-image" />
          <div class="overlay">
            <button class="replace-btn" @click.stop="triggerFileInput">
              <IconSvg name="refresh" />
              <span class="tooltip">更换图片</span>
            </button>
          </div>
        </div>

        <!-- 上传提示（无预览时显示） -->
        <div v-else class="upload-content">
          <div class="upload-icon">
            <IconSvg name="uploadImage" color="#ffd700" />
          </div>
          <div class="upload-text">
            <p class="primary-text">点击或拖拽图片到此处</p>
            <p class="secondary-text">
              支持 JPG、PNG 格式，最大 {{ maxSizeText
              }}<span v-if="multiple">，可多选</span>
            </p>
          </div>
        </div>
      </div>

      <!-- 多图模式预览列表 -->
      <div v-if="multiple && previewList.length" class="multi-preview-list">
        <div
          v-for="(item, idx) in previewList"
          :key="idx"
          class="preview-item"
          :class="{ selected: selectedIndex === idx }"
          @click="selectFile(idx)"
        >
          <div class="thumbnail-container">
            <img :src="item.url" alt="预览图" class="thumbnail" />

            <div class="item-actions">
              <button
                class="action-btn preview-btn"
                @mouseenter="showPreview(item.url)"
                @mouseleave="hidePreview"
              >
                <IconSvg name="eye" />
              </button>
              <button
                class="action-btn delete-btn"
                @click.stop="removeFile(idx)"
              >
                <IconSvg name="delete" />
              </button>
            </div>
          </div>

          <div class="file-info">
            <div class="file-name">{{ truncateFileName(item.name) }}</div>
            <div class="file-size">{{ formatFileSize(item.size) }}</div>
          </div>
        </div>
        <!-- 浮动预览大图 -->
        <div
          v-if="largePreviewVisible"
          class="large-preview"
          @mouseenter="keepPreviewVisible"
          @mouseleave="hidePreview"
        >
          <img :src="largePreviewUrl" alt="大图预览" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
// 组件属性定义（接收父组件传递的参数）
const props = defineProps<{
  maxSize?: number; // 允许上传的最大文件大小（单位：字节，默认10MB）
  multiple?: boolean; // 是否支持多文件上传模式
}>();

// 自定义事件定义（向父组件触发的事件）
const emit = defineEmits<{
  (e: 'file-upload', files: File[]): void; // 文件上传完成时触发，返回文件数组
  (e: 'file-removed'): void; // 当所有文件被移除时触发
  (e: 'file-selected', file: File | null, index: number): void; // 文件选中状态变化时触发
}>();

// DOM元素引用（文件输入框的模板引用）
const fileInput = ref<HTMLInputElement | null>(null);

// 响应式状态管理
const isDragover = ref(false); // 拖拽悬停状态标识
const previewList = ref<
  // 预览文件列表，存储文件信息和预览URL
  { url: string; name: string; size: number; file: File }[]
>([]);
const selectedIndex = ref(0); // 当前选中文件的索引
const largePreviewVisible = ref(false); // 大图预览可见性控制
const largePreviewUrl = ref(''); // 大图预览的URL地址
let previewTimer: ReturnType<typeof setTimeout> | null = null; // 预览计时器

// 计算属性（根据props计算是否多选模式）
const multiple = computed(() => props.multiple ?? false);
// 最大文件尺寸计算（优先使用props传入的值，默认5MB）
const MAX_FILE_SIZE = props.maxSize || 5 * 1024 * 1024;
// 计算属性：格式化显示最大文件大小
const maxSizeText = computed(() => formatFileSize(MAX_FILE_SIZE));

/* 文件大小格式化函数：将字节转换为易读格式 */
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/* 触发隐藏的文件选择对话框 */
const triggerFileInput = () => {
  fileInput.value?.click();
};

/* 处理文件选择变化事件 */
const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    // 将FileList转换为数组并处理
    processFiles(Array.from(input.files));

    // 默认选中第一个文件
    if (multiple.value && previewList.value.length) {
      selectedIndex.value = 0;
      emitSelectedChange(0);
    }
  }
};

/* 处理拖拽进入事件 */
const handleDragEnter = () => {
  isDragover.value = true;
};

/* 处理拖拽悬停事件 */
const handleDragOver = () => {
  isDragover.value = true;
};

/* 处理拖拽离开事件 */
const handleDragLeave = () => {
  isDragover.value = false;
};

/* 处理文件拖放事件 */
const handleDrop = (event: DragEvent) => {
  isDragover.value = false;
  const files = event.dataTransfer?.files;
  if (files && files.length > 0) {
    processFiles(Array.from(files));

    // 默认选中第一个文件
    if (multiple.value && previewList.value.length) {
      selectedIndex.value = 0;
      emitSelectedChange(0);
    }
  }
};

/* 处理并验证文件列表 */
const processFiles = (files: File[]) => {
  const validFiles: File[] = []; // 有效文件临时存储

  // 单文件模式处理
  if (!multiple.value) {
    // 只取第一个文件
    files = files.slice(0, 1);
    // 清除现有预览（释放内存）
    previewList.value.forEach(
      (item) => item.url && URL.revokeObjectURL(item.url)
    );
    previewList.value = [];
  }

  // 遍历处理每个文件
  files.forEach((file) => {
    // 文件类型验证（必须是图片）
    if (!file.type.startsWith('image/')) {
      useMessage({
        name: '无图片',
        description: '请上传图片文件',
        type: 'error',
      });
      return;
    }
    // 文件大小验证
    if (file.size > MAX_FILE_SIZE) {
      useMessage({
        name: '文件过大',
        description: `文件大小不能超过${formatFileSize(MAX_FILE_SIZE)}`,
        type: 'error',
      });
      return;
    }

    // 生成预览URL（避免内存泄漏，在移除时会释放）
    const url = URL.createObjectURL(file);
    // 添加到预览列表
    previewList.value.push({
      url,
      name: file.name,
      size: file.size,
      file,
    });
    validFiles.push(file);
  });

  // 如果有有效文件，触发事件
  if (validFiles.length) {
    emit(
      'file-upload',
      previewList.value.map((item) => item.file) // 发送原始File对象数组
    );
  }
};

/* 选择文件 */
const selectFile = (index: number) => {
  selectedIndex.value = index;
  emitSelectedChange(index);
};

/* 发送选中变化事件 */
const emitSelectedChange = (index: number) => {
  // 确定文件存在时才发送事件
  if (!previewList.value[index]) return;
  const file = index >= 0 ? previewList.value[index].file : null;
  emit('file-selected', file, index);
};

/* 移除指定索引的文件 */
const removeFile = (idx: number) => {
  const item = previewList.value[idx];
  // 释放创建的Object URL（重要：避免内存泄漏）
  if (item && item.url) {
    URL.revokeObjectURL(item.url);
  }

  // 处理选中状态
  if (selectedIndex.value === idx) {
    if (previewList.value.length > 1) {
      // 如果删除的是当前选中项，且还有其他文件
      selectedIndex.value = idx === 0 ? 0 : idx - 1;
    } else {
      selectedIndex.value = -1;
    }
  } else if (selectedIndex.value > idx) {
    // 如果删除的是当前选中项之前的项
    selectedIndex.value--;
  }

  // 从列表中移除
  previewList.value.splice(idx, 1);

  // 发送事件
  if (previewList.value.length) {
    // 有文件剩余
    emit(
      'file-upload',
      previewList.value.map((item) => item.file)
    );
    emitSelectedChange(selectedIndex.value);
  } else {
    // 所有文件都被移除
    emit('file-removed');
    emitSelectedChange(-1);
  }
};

/* 显示大图预览 */
const showPreview = (url: string) => {
  // 清除之前的定时器
  if (previewTimer) {
    clearTimeout(previewTimer);
    previewTimer = null;
  }

  largePreviewUrl.value = url;
  largePreviewVisible.value = true;
};

/* 保持预览可见（当鼠标进入预览区域时） */
const keepPreviewVisible = () => {
  // 清除隐藏定时器
  if (previewTimer) {
    clearTimeout(previewTimer);
    previewTimer = null;
  }
};

/* 隐藏大图预览 */
const hidePreview = () => {
  // 设置延迟隐藏，避免立即消失导致的闪烁
  previewTimer = setTimeout(() => {
    largePreviewVisible.value = false;
  }, 300);
};

/* 截断文件名 */
const truncateFileName = (name: string, maxLength = 12) => {
  if (name.length <= maxLength) return name;
  return `${name.substring(0, maxLength - 3)}...`;
};

// 暴露组件方法给父组件
defineExpose({
  removeFile, // 允许父组件直接调用文件移除方法
});
</script>

<style scoped lang="scss">
.rikka-uploader {
  width: 100%;
  position: relative;
}

.upload-container {
  display: flex;
  gap: 1.5rem;
  align-items: flex-start;
}

.upload-area {
  border: 2px dashed var(--upload-border);
  border-radius: 0.5rem;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--upload-surface);
  position: relative;
  overflow: hidden;
  height: 15rem;
  flex-shrink: 0;
  flex: 1;

  // 魔法阵背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
      radial-gradient(
        circle at center,
        transparent 60%,
        rgba(143, 51, 255, 0.1) 100%
      ),
      repeating-linear-gradient(
        45deg,
        transparent,
        transparent 1rem,
        rgba(106, 48, 147, 0.1) 1rem,
        rgba(106, 48, 147, 0.1) 2rem
      );
    z-index: 0;
    opacity: 0.6;
  }

  &:hover {
    border-color: var(--upload-primary);
    background: var(--upload-bg);
    box-shadow: 0 0 1.5rem rgba(143, 51, 255, 0.5);
  }

  &.is-dragover {
    border-color: var(--upload-primary);
    background: var(--upload-bg);
    box-shadow: 0 0 2rem rgba(143, 51, 255, 0.7);

    .upload-icon {
      animation: pulse 1.5s infinite;
    }
  }

  &.has-preview {
    padding: 0;
    border: 1px solid var(--upload-primary);
    box-shadow: 0 0 1rem rgba(143, 51, 255, 0.4);
  }

  .file-input {
    display: none;
  }

  .upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
    height: 100%;

    .upload-icon {
      width: 3rem;
      height: 3rem;
      color: var(--upload-primary);
      filter: drop-shadow(0 0 0.25rem rgba(143, 51, 255, 0.7));

      svg {
        width: 100%;
        height: 100%;
      }
    }

    .upload-text {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .primary-text {
        font-size: 1.1rem;
        color: var(--upload-text);
        margin: 0;
        font-weight: bold;
        text-shadow: 0 0 0.125rem rgba(255, 255, 255, 0.3);
      }

      .secondary-text {
        font-size: 0.9rem;
        color: var(--upload-text-light);
        margin: 0;
      }
    }
  }

  .single-preview {
    position: relative;
    width: 100%;
    height: 100%;

    .preview-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 0.375rem;
    }

    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.4);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      border-radius: 0.375rem;

      &:hover {
        opacity: 1;
      }
    }

    .replace-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.5);
      border-radius: 50%;
      width: 2.5rem;
      height: 2.5rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      backdrop-filter: blur(0.25rem);
      transition: all 0.3s ease;
      position: relative;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);

        .tooltip {
          opacity: 1;
          transform: translateY(0);
        }
      }

      svg {
        width: 1.5rem;
        height: 1.5rem;
        fill: white;
      }

      .tooltip {
        position: absolute;
        bottom: -1.875rem;
        left: 50%;
        transform: translateX(-50%) translateY(0.625rem);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        white-space: nowrap;
        opacity: 0;
        transition: all 0.3s ease;
        pointer-events: none;
      }
    }
  }
}

.multi-preview-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 15rem;
  overflow-y: auto;
  padding-right: 0.3125rem;
  width: 13.75rem;
  position: relative;

  &::-webkit-scrollbar {
    width: 0.375rem;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--upload-primary);
    border-radius: 0.1875rem;
  }

  .preview-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.3125rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    background: var(--upload-surface);
    border: 1px solid transparent;
    position: relative;

    &:hover {
      background: rgba(143, 51, 255, 0.1);

      .thumbnail-container .item-actions {
        opacity: 1;
      }
    }

    &.selected {
      border-color: var(--upload-primary);
      background: rgba(143, 51, 255, 0.15);
      box-shadow: 0 0 0.5rem rgba(143, 51, 255, 0.3);
    }

    .thumbnail-container {
      position: relative;
      width: 3.125rem;
      height: 3.125rem;
      flex-shrink: 0;

      .thumbnail {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 0.25rem;
        border: 1px solid rgba(255, 255, 255, 0.1);
      }

      .item-actions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.3125rem;
        opacity: 0;
        transition: opacity 0.3s ease;
        border-radius: 0.25rem;
      }

      .action-btn {
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;

        svg {
          width: 0.875rem;
          height: 0.875rem;
          fill: white;
        }

        &.preview-btn {
          background: rgba(255, 255, 255, 0.2);

          &:hover {
            background: var(--upload-primary);
            transform: scale(1.1);
          }
        }

        &.delete-btn {
          background: rgba(211, 15, 57, 0.2);

          &:hover {
            background: var(--upload-danger);
            transform: scale(1.1);
          }
        }
      }
    }

    .file-info {
      flex: 1;
      min-width: 0;

      .file-name {
        font-size: 0.9rem;
        color: var(--upload-text);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
      }

      .file-size {
        font-size: 0.75rem;
        color: var(--upload-text-light);
      }
    }
  }
}

.large-preview {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 100;
  border: 1px solid var(--upload-border);
  border-radius: 0.375rem;
  box-shadow: 0 0.25rem 1.25rem rgba(0, 0, 0, 0.3);
  width: 18.75rem;
  height: 12.5rem;
  background: black;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

// 魔法脉动动画
@keyframes pulse {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 0.25rem rgba(143, 51, 255, 0.7));
  }
  50% {
    transform: scale(1.1);
    filter: drop-shadow(0 0 0.625rem rgba(143, 51, 255, 0.9));
  }
  100% {
    transform: scale(1);
    filter: drop-shadow(0 0 0.25rem rgba(143, 51, 255, 0.7));
  }
}
</style>

