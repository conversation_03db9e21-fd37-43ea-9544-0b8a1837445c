<template>
  <div class="page-container">
    <Head>
      <Title>图片搜索-{{ key }}</Title>
    </Head>
    <!-- 瀑布流图片画廊组件 -->
    <RikkaImagesWaterfallGallery
      :items="imagesArray"
      :columns="0"
      :card-width="300"
      :gap="20"
      :loading="loading"
      :has-more="hasMore"
      @load-more="handleLoadMore"
    >
    </RikkaImagesWaterfallGallery>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'search',
});

const route = useRoute();
const key = computed(() => route.query.key as string);

// 响应式图片数组
const imagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
// 加载状态
const loading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMore = computed(
  () => paginated.value.page < paginated.value.totalPage
);
// 分页信息
const paginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

/**
 * 获取图片列表数据
 * @param query 可选查询参数
 */
const getPhotosList = async (query?: PhotosListQuery) => {
  try {
    const { list, ...res } = await useApi().getPhotosList(query);
    imagesArray.value.push(...list);
    paginated.value = res;
  } catch (err) {
    throw err;
  }
};

/**
 * 处理加载更多数据的逻辑
 */
const handleLoadMore = async () => {
  if (loading.value || !hasMore.value) {
    return;
  }
  loading.value = true;

  await getPhotosList({
    page: paginated.value.page + 1,
    pageSize: 20,
    keyword: key.value,
  });
  loading.value = false;
};

watch(key, async () => {
  imagesArray.value = [];

  await getPhotosList({
    page: 1,
    pageSize: 20,
    keyword: key.value,
  });
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
