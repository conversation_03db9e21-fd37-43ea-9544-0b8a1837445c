<template>
  <div class="page-container">
    <Head>
      <Title>次元回廊-关注</Title>
    </Head>
    <CardContainer>
      <CardBody
        class="group/card relative size-auto rounded-xl border border-black/[0.1] bg-gray-50 p-6 sm:w-[30rem] dark:border-white/[0.2] dark:bg-black dark:hover:shadow-2xl dark:hover:shadow-emerald-500/[0.1]"
      >
        <CardItem
          :translate-z="50"
          class="text-xl font-bold text-neutral-600 dark:text-white"
        >
          关注推荐页面尚未开发
        </CardItem>
        <CardItem
          as="p"
          translate-z="60"
          class="mt-2 max-w-sm text-sm text-neutral-500 dark:text-neutral-300"
        >
          尽情期待
        </CardItem>
        <CardItem :translate-z="100" class="mt-4 w-full">
          <img
            src="https://file.sixflower.top/images/img-175120430203747vigb.jpg"
            height="1000"
            width="1000"
            class="h-60 w-full rounded-xl object-cover group-hover/card:shadow-xl"
            alt="thumbnail"
          />
        </CardItem>
      </CardBody>
    </CardContainer>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'home',
});
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

