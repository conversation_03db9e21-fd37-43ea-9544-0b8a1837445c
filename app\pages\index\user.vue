<template>
  <div class="user-page-container">
    <Head>
      <Title>{{ userDetail?.nickname || '用户' }}主页</Title>
    </Head>
    <!-- 用户信息头部区域 -->
    <div class="user-header" v-if="userDetail">
      <!-- 背景图 -->
      <div
        class="background"
        :style="{
          backgroundImage: `linear-gradient(to left, #fff0, var(--background-elevated)), url(${userDetail.background + '?width=1600'})`,
        }"
      ></div>

      <!-- 用户基本信息 -->
      <div class="user-info">
        <!-- 用户头像 -->
        <img :src="userDetail.avatar" alt="用户头像" class="avatar" />

        <!-- 用户详细信息 -->
        <div class="info">
          <!-- 昵称 -->
          <div class="nickname">{{ userDetail.nickname }}</div>

          <!-- 关注/粉丝/热度数据 -->
          <div class="follow-fans">
            <div class="follow">
              <span>关注</span> {{ userDetail.followingsCount }}
            </div>
            <div class="fans">
              <span>粉丝</span> {{ userDetail.followersCount }}
            </div>
            <div class="fans">
              <span>热度</span> {{ userDetail.totalDownloads }}
            </div>
          </div>

          <!-- UID和性别年龄 -->
          <div class="uid-gender">
            <div class="uid">
              UID: <span>{{ userDetail.uid }}</span>
            </div>
            <div class="gender-age" v-if="userDetail.gender">
              <IconSvg
                :name="userGender.name"
                size="1.2rem"
                :color="userGender.color"
              />
              <span v-if="userDetail.birthday">{{ userAge }}岁</span>
            </div>
          </div>

          <!-- 个人简介 -->
          <div class="bio">
            {{ userDetail.bio || '这个人很懒，什么都没留下' }}
          </div>
        </div>
      </div>

      <!-- 关注按钮 -->
      <RippleButton
        class="follow-button"
        :class="{ active: userDetail.isFollowing }"
        :disabled="!authStore.isAuthenticated"
        @click="toggleFollow"
      >
        {{ userDetail.isFollowing ? '已关注' : '关注' }}
      </RippleButton>
    </div>

    <!-- 标签页区域 -->
    <div class="tabs" v-if="userDetail && tabs.length > 0">
      <RikkaTabs
        :tabs="tabs"
        :initial-tab="0"
        @active-tab="
          (index) => {
            tabStore.setUserTab(index);
            activeTabIndex = index;
          }
        "
      >
        <template #user-images>
          <RikkaImagesWaterfallGallery
            :items="userImagesArray"
            :columns="0"
            :card-width="250"
            :gap="20"
            :loading="userImagesLoading"
            :has-more="hasMoreUserImages"
            @load-more="handleLoadMoreUserImages"
          />
        </template>
        <template #user-posts>
          <RikkaPostsWaterfallGallery
            :posts="userPostsArray"
            :columns="0"
            :card-width="250"
            :gap="20"
            :loading="userPostsLoading"
            :has-more="hasMoreUserPosts"
            @load-more="handleLoadMoreUserPosts"
          />
        </template>
        <template #liked-images>
          <RikkaImagesWaterfallGallery
            :items="likedImagesArray"
            :columns="0"
            :card-width="250"
            :gap="20"
            :loading="likedImagesLoading"
            :has-more="hasMoreLikedImages"
            @load-more="handleLoadMoreLikedImages"
          />
        </template>
        <template #collected-images>
          <RikkaImagesWaterfallGallery
            :items="collectedImagesArray"
            :columns="0"
            :card-width="250"
            :gap="20"
            :loading="collectedImagesLoading"
            :has-more="hasMoreCollectedImages"
            @load-more="handleLoadMoreCollectedImages"
          />
        </template>
        <template #liked-posts>
          <RikkaPostsWaterfallGallery
            :posts="likedPostsArray"
            :columns="0"
            :card-width="250"
            :gap="20"
            :loading="likedPostsLoading"
            :has-more="hasMoreLikedPosts"
            @load-more="handleLoadMoreLikedPosts"
          />
        </template>
        <template #collected-posts>
          <RikkaPostsWaterfallGallery
            :posts="collectedPostsArray"
            :columns="0"
            :card-width="250"
            :gap="20"
            :loading="collectedPostsLoading"
            :has-more="hasMoreCollectedPosts"
            @load-more="handleLoadMoreCollectedPosts"
          />
        </template>
      </RikkaTabs>
    </div>

    <!-- 没有可见tab时的提示 -->
    <div v-if="userDetail && tabs.length === 0" class="no-tabs-container">
      <div class="no-tabs-message">
        <div>该用户设置了隐私保护</div>
        <div>暂时无法查看相关内容</div>
      </div>
    </div>

    <!-- 加载中状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <div>加载用户信息中...</div>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-container">
      <div>获取用户信息失败</div>
      <RippleButton @click="getUserDetail(uid)">重试</RippleButton>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'details',
});

const route = useRoute();
const router = useRouter();
const uid = route.query.uid as string;
const authStore = useAuthStore();
const tabStore = useTabStore();
const loading = ref(true);
const error = ref(false);

// 用户详情
const userDetail = ref<OtherUserInfo>();
if (uid === authStore.authStore?.uid) router.replace('/mine');

// 获取用户详情
const getUserDetail = async (id: string) => {
  if (!id) {
    error.value = true;
    loading.value = false;
    useMessage({
      name: '参数错误',
      description: '缺少用户ID',
      type: 'error',
    });
    return;
  }

  loading.value = true;
  error.value = false;

  try {
    const data = await useApi().getOtherUserInfo(id);
    userDetail.value = data;
    await getSetting(data.uid);

    // 清空所有数据数组，避免重复数据
    userImagesArray.value = [];
    userPostsArray.value = [];
    likedImagesArray.value = [];
    collectedImagesArray.value = [];
    likedPostsArray.value = [];
    collectedPostsArray.value = [];

    // 重置分页信息
    userImagesPaginated.value = {
      page: 0,
      pageSize: 10,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'asc',
    };
    userPostsPaginated.value = {
      page: 0,
      pageSize: 10,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'asc',
    };
    likedImagesPaginated.value = {
      page: 0,
      pageSize: 10,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'asc',
    };
    collectedImagesPaginated.value = {
      page: 0,
      pageSize: 10,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'asc',
    };
    likedPostsPaginated.value = {
      page: 0,
      pageSize: 10,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'asc',
    };
    collectedPostsPaginated.value = {
      page: 0,
      pageSize: 10,
      totalCount: 0,
      totalPage: 1,
      sortField: 'createTime',
      sortOrder: 'asc',
    };

    // 重置tab状态到第一个tab
    tabStore.setUserTab(0);
    activeTabIndex.value = 0;
  } catch (err) {
    error.value = true;
    console.error('获取用户信息失败', err);
    useMessage({
      name: '加载失败',
      description: '无法获取用户信息',
      type: 'error',
    });
  } finally {
    loading.value = false;
  }
};

// 计算用户性别图标和颜色
const userGender = computed<{
  color: string;
  name: 'male' | 'female' | 'hidden';
}>(() => {
  switch (userDetail.value?.gender) {
    case 'male':
      return {
        color: '#4285F4',
        name: 'male',
      };
    case 'female':
      return {
        color: '#DB4437',
        name: 'female',
      };
    default:
      return {
        color: '#fff',
        name: 'hidden',
      };
  }
});

// 计算用户年龄
const userAge = computed(() => {
  if (!userDetail.value?.birthday) return null;

  const birthDate = new Date(userDetail.value.birthday);
  const today = new Date();
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
});

// 关注按钮点击事件
const toggleFollow = async () => {
  if (!userDetail.value || !authStore.isAuthenticated) return;

  try {
    await useApi().followUser(userDetail.value.uid);
    userDetail.value.isFollowing = !userDetail.value.isFollowing;

    // 更新关注者数量
    if (userDetail.value.isFollowing) {
      userDetail.value.followersCount++;
    } else {
      userDetail.value.followersCount = Math.max(
        0,
        userDetail.value.followersCount - 1
      );
    }

    useMessage({
      name: userDetail.value.isFollowing ? '关注成功' : '已取消关注',
      description: '',
      type: 'success',
    });
  } catch (err) {
    throw err;
  }
};

// 定义用户设置的数据模型，默认值为 false
const settings = ref<UserSettings>({
  showFollowList: false,
  showFansList: false,
  showFavoritePhotoList: false,
  showLikePhotoList: false,
  showFavoriteContentList: false,
  showLikeContentList: false,
  showMyPhotoList: false,
  showMyContentList: false,
});

// 异步获取用户当前设置的方法
const getSetting = async (uid: string) => {
  settings.value = await useApi().getUserSetting(uid);
};

// 标签页配置 - 参考mine页面的实现方式，根据隐私设置动态显示
const tabs = computed(() => {
  const tabList = [];

  // 用户图片 - 根据隐私设置显示
  if (settings.value.showMyPhotoList) {
    tabList.push({
      title: '用户图片 ' + (userDetail.value?.photosCount || 0),
      slotName: 'user-images',
    });
  }

  // 用户帖子 - 根据隐私设置显示
  if (settings.value.showMyContentList) {
    tabList.push({
      title: '用户帖子 ' + (userDetail.value?.contentsCount || 0),
      slotName: 'user-posts',
    });
  }

  // 点赞图片 - 根据隐私设置显示
  if (settings.value.showLikePhotoList) {
    tabList.push({
      title: '点赞图片 ' + (userDetail.value?.likePhotosCount || 0),
      slotName: 'liked-images',
    });
  }

  // 收藏图片 - 根据隐私设置显示
  if (settings.value.showFavoritePhotoList) {
    tabList.push({
      title: '收藏图片 ' + (userDetail.value?.favoritePhotosCount || 0),
      slotName: 'collected-images',
    });
  }

  // 点赞帖子 - 根据隐私设置显示
  if (settings.value.showLikeContentList) {
    tabList.push({
      title: '点赞帖子 ' + (userDetail.value?.likeContentsCount || 0),
      slotName: 'liked-posts',
    });
  }

  // 收藏帖子 - 根据隐私设置显示
  if (settings.value.showFavoriteContentList) {
    tabList.push({
      title: '收藏帖子 ' + (userDetail.value?.favoriteContentsCount || 0),
      slotName: 'collected-posts',
    });
  }

  return tabList;
});

// 当前活跃的tab索引，初始化为store中保存的值
const activeTabIndex = ref(tabStore.getUserTab());

// 用户图片列表
const userImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const userImagesLoading = ref(false);
const userImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});
const hasMoreUserImages = computed(
  () => userImagesPaginated.value.page < userImagesPaginated.value.totalPage
);
const getUserImagesList = async (query?: PhotosListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPhotosList(
      userDetail.value.uid,
      query
    );
    userImagesArray.value.push(...list);
    userImagesPaginated.value = res;
  } catch (err) {
    userImagesLoading.value = false;
    userImagesPaginated.value.totalPage = 0;
    return;
  }
};
const handleLoadMoreUserImages = async () => {
  if (userImagesLoading.value || !hasMoreUserImages.value) return;
  userImagesLoading.value = true;
  await getUserImagesList({
    page: userImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  userImagesLoading.value = false;
};

// 用户帖子列表
const userPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const userPostsLoading = ref(false);
const userPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});
const hasMoreUserPosts = computed(
  () => userPostsPaginated.value.page < userPostsPaginated.value.totalPage
);
const getUserPostsList = async (query?: PostsListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useApi().getUserPostList(
      userDetail.value.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    userPostsArray.value.push(...newData);
    userPostsPaginated.value = res;
  } catch (err) {
    userPostsLoading.value = false;
    userPostsPaginated.value.totalPage = 0;
    return;
  }
};
const handleLoadMoreUserPosts = async () => {
  if (userPostsLoading.value || !hasMoreUserPosts.value) return;
  userPostsLoading.value = true;
  await getUserPostsList({
    page: userPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  userPostsLoading.value = false;
};

// 点赞图片列表
const likedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const likedImagesLoading = ref(false);
const likedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});
const hasMoreLikedImages = computed(
  () => likedImagesPaginated.value.page < likedImagesPaginated.value.totalPage
);
const getLikedImagesList = async (query?: PostsListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikePhotoList(
      userDetail.value.uid,
      query
    );
    likedImagesArray.value.push(...list);
    likedImagesPaginated.value = res;
  } catch (err) {
    likedImagesLoading.value = false;
    likedImagesPaginated.value.totalPage = 0;
    return;
  }
};
const handleLoadMoreLikedImages = async () => {
  if (likedImagesLoading.value || !hasMoreLikedImages.value) return;
  likedImagesLoading.value = true;
  await getLikedImagesList({
    page: likedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  likedImagesLoading.value = false;
};

// 收藏图片列表
const collectedImagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
const collectedImagesLoading = ref(false);
const collectedImagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});
const hasMoreCollectedImages = computed(
  () =>
    collectedImagesPaginated.value.page <
    collectedImagesPaginated.value.totalPage
);
const getCollectedImagesList = async (query?: PostsListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getFavoritePhotoList(
      userDetail.value.uid,
      query
    );
    collectedImagesArray.value.push(...list);
    collectedImagesPaginated.value = res;
  } catch (err) {
    collectedImagesLoading.value = false;
    collectedImagesPaginated.value.totalPage = 0;
    return;
  }
};
const handleLoadMoreCollectedImages = async () => {
  if (collectedImagesLoading.value || !hasMoreCollectedImages.value) return;
  collectedImagesLoading.value = true;
  await getCollectedImagesList({
    page: collectedImagesPaginated.value.page + 1,
    pageSize: 20,
  });
  collectedImagesLoading.value = false;
};

// 点赞帖子列表
const likedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const likedPostsLoading = ref(false);
const likedPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});
const hasMoreLikedPosts = computed(
  () => likedPostsPaginated.value.page < likedPostsPaginated.value.totalPage
);
const getLikedPostsList = async (query?: PostsListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getLikeContentList(
      userDetail.value.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    likedPostsArray.value.push(...newData);
    likedPostsPaginated.value = res;
  } catch (err) {
    likedPostsLoading.value = false;
    likedPostsPaginated.value.totalPage = 0;
    return;
  }
};
const handleLoadMoreLikedPosts = async () => {
  if (likedPostsLoading.value || !hasMoreLikedPosts.value) return;
  likedPostsLoading.value = true;
  await getLikedPostsList({
    page: likedPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  likedPostsLoading.value = false;
};

// 收藏帖子列表
const collectedPostsArray = ref<PostsWaterfallGalleryItem[]>([]);
const collectedPostsLoading = ref(false);
const collectedPostsPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});
const hasMoreCollectedPosts = computed(
  () =>
    collectedPostsPaginated.value.page < collectedPostsPaginated.value.totalPage
);
const getCollectedPostsList = async (query?: PostsListQuery) => {
  if (!userDetail.value?.uid) return;
  try {
    const { list, ...res } = await useUserApi().getFavoriteContentList(
      userDetail.value.uid,
      query
    );
    const newData = await Promise.all(
      list.map(async (v: any) => {
        const { photos, ...rest } = v;
        if (!photos || photos.length === 0 || !photos[0]) return v;
        return {
          ...rest,
          photos,
          cover: {
            url: photos[0].url,
            filename: photos[0].filename,
            width: photos[0].attributes.width,
            height: photos[0].attributes.height,
          },
        };
      })
    );
    collectedPostsArray.value.push(...newData);
    collectedPostsPaginated.value = res;
  } catch (err) {
    collectedPostsLoading.value = false;
    collectedPostsPaginated.value.totalPage = 0;
    return;
  }
};
const handleLoadMoreCollectedPosts = async () => {
  if (collectedPostsLoading.value || !hasMoreCollectedPosts.value) return;
  collectedPostsLoading.value = true;
  await getCollectedPostsList({
    page: collectedPostsPaginated.value.page + 1,
    pageSize: 20,
  });
  collectedPostsLoading.value = false;
};

getUserDetail(uid);

// 监听路由变化，当UID变化时重新加载
watch(
  () => route.query.uid,
  (newUid) => {
    if (newUid && newUid !== uid) {
      getUserDetail(newUid as string);
    }
  }
);
</script>

<style lang="scss" scoped>
.user-page-container {
  display: flex;
  flex-direction: column;

  .user-header {
    position: relative;
    border-radius: 1rem;
    overflow: visible;
    background-color: var(--background-secondary);
    box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.1);

    .background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 100%;
      background-size: cover;
      background-position: center;
      z-index: 1;
    }

    .avatar {
      width: 7rem;
      height: 7rem;
      border-radius: 50%;
      border: 0.25rem solid var(--background-base);
      box-shadow: 0 0.25rem 1rem rgba(0, 0, 0, 0.2);
      z-index: 3;
    }

    .user-info {
      position: relative;
      z-index: 2;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 2.5rem 1.5rem 1.5rem 1.5rem;

      > img {
        width: 8rem;
        height: 8rem;
        object-fit: cover;
        object-position: center;
        transition: var(--transition);
        border-radius: 50%;
      }

      .info {
        flex: 1;
        margin-top: 1.5rem;

        .nickname {
          font-size: 1.5rem;
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .follow-fans {
          display: flex;
          margin: 0.3rem 0 0.7rem;

          > div {
            margin-right: 2rem;
            cursor: pointer;

            > span {
              color: var(--text-secondary);
              margin-right: 0.2rem;
            }

            &:hover {
              > span {
                color: var(--text-primary);
              }
            }
          }
        }

        .uid-gender {
          font-size: 0.8rem;
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;

          > .uid {
            color: var(--text-secondary);
            margin-right: 2rem;

            > span {
              margin-left: 0.1rem;
            }
          }

          > .gender-age {
            padding: 0.2rem 0.4rem;
            border-radius: 0.4rem;
            background-color: var(--background-floating);
            display: flex;
            align-items: center;

            > span {
              margin-left: 0.2rem;
            }
          }
        }

        .bio {
          font-size: 0.9rem;
          color: var(--text-secondary);
          margin-top: 0.5rem;
          line-height: 1.4;
          max-width: 40rem;
        }
      }
    }

    .follow-button {
      position: absolute;
      top: 2.2rem;
      right: 2.2rem;
      z-index: 3;
      padding: 0.5rem 1.5rem;
      border-radius: 2rem;
      font-size: 0.9rem;
      background: linear-gradient(135deg, #6366f1, #8b5cf6);
      color: white;

      &.active {
        background: var(--background-floating);
        color: var(--text-secondary);
      }

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }
    }
  }

  .tabs {
    min-height: 44rem;

    :deep(.tabs-container) {
      border-radius: 0 0 1rem 1rem;
    }
  }

  .no-tabs-container {
    flex: 1;
    background-color: var(--background-secondary);
    border-radius: 0 0 1rem 1rem;
    box-shadow: 0 0.15rem 0.5rem rgba(0, 0, 0, 0.08);
    display: flex;
    align-items: center;
    justify-content: center;

    .no-tabs-message {
      text-align: center;
      color: var(--text-secondary);
      font-size: 1.1rem;
      line-height: 1.6;

      div:first-child {
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
    }
  }

  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    height: 20rem;
    color: var(--text-secondary);

    .loading-spinner {
      width: 3rem;
      height: 3rem;
      border: 0.25rem solid rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      border-top-color: var(--text-secondary);
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@media (max-width: 768px) {
  .user-page-container {
    .user-header {
      .avatar {
        width: 5rem;
        height: 5rem;
        bottom: -2.5rem;
      }
      .user-info {
        padding-top: 7.5rem;
      }
      .info {
        .follow-fans {
          justify-content: center;
        }

        .uid-gender {
          justify-content: center;
        }
      }
    }

    .follow-button {
      top: auto;
      bottom: 1.5rem;
      right: 1.5rem;
    }
  }
}
</style>
