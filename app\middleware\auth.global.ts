/**
 * 全局路由认证中间件
 * 用于处理需要认证的路由访问控制
 */

/**
 * 定义Nuxt全局路由中间件
 * @param to - 目标路由对象，包含路径、参数等信息
 */
export default defineNuxtRouteMiddleware((to) => {
  // 从认证状态存储中获取登录状态检查函数
  const { getIsLogin } = useAuthStore();

  /**
   * 需要登录保护的页面路径白名单
   * @type {string[]}
   */
  const protectedRoutes: string[] = [];

  // 获取路由状态存储实例，用于持久化存储当前路径
  const routerStore = useRouterStore();

  // 构建完整路径，包含查询参数
  const fullPath = to.fullPath;

  /**
   * 路径规范化函数：去除路径末尾的斜杠
   * @param {string} path - 需要规范化的路径
   * @returns {string} 规范化后的路径
   */
  const normalizePath = (path: string): string => {
    // 如果路径长度大于1且以斜杠结尾，则去除末尾斜杠
    if (path.length > 1 && path.endsWith('/')) {
      return path.slice(0, -1);
    }
    return path;
  };

  // 规范化完整路径
  const normalizedFullPath = normalizePath(fullPath);

  // 如果路径发生了变化（即去除了末尾斜杠），则重定向到规范化路径
  if (normalizedFullPath !== fullPath) {
    return navigateTo(normalizedFullPath, { redirectCode: 301 });
  }

  /**
   * 检查用户是否未登录且访问的是受保护路由
   * 如果满足条件则重定向到认证页面
   */
  if (!getIsLogin() && protectedRoutes.includes(to.path)) {
    // 存储目标路径到路由状态
    routerStore.set('/auth');

    // 重定向到认证页面
    return navigateTo('/auth');
  }

  // 对于非受保护路由，正常存储当前完整路径（包含查询参数）
  routerStore.set(normalizedFullPath);
});
