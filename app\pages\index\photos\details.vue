<template>
  <div class="photo-details">
    <Head>
      <Title>{{ photoDetail?.filename || '图片详情' }}</Title>
      <Meta
        name="keywords"
        :content="
          photoDetail?.tags.join(',') +
          ',' +
          photoDetail?.category +
          ',' +
          ',次元回廊,Dimensional Corridor,二次元,图片分享,交流平台,交友,壁纸'
        "
      />
    </Head>
    <!-- 主内容区 -->
    <div class="content-container">
      <!-- 左侧图片展示区 -->
      <div class="photo-display" ref="photoDisplayRef">
        <div class="image-wrapper" v-if="photoDetail">
          <img
            :src="photoDetail.url + '?width=800'"
            :alt="photoDetail.filename"
            class="main-image"
            :class="{ loaded: imageLoaded }"
            @click="openFullscreen"
            :style="imageStyle"
            @load="onImageLoad"
          />
        </div>
        <div v-else class="image-placeholder">加载中...</div>

        <!-- 图片操作按钮 -->
        <div class="image-actions">
          <div class="action-button" @click="downloadImage">
            <IconSvg name="download" :size="20" color="#FFFFFF" />
          </div>
          <div class="action-button" @click="toggleFavorite">
            <IconSvg
              name="favorite"
              :size="20"
              :color="photoDetail?.isFavorited ? '#FFD700' : '#FFFFFF'"
            />
          </div>
          <div class="action-button" @click="toggleLike">
            <IconSvg
              name="like"
              :size="20"
              :color="photoDetail?.isLiked ? '#FF5252' : '#FFFFFF'"
            />
          </div>
        </div>
      </div>

      <!-- 右侧信息区 -->
      <div class="photo-info-simple">
        <!-- 基础信息分组 -->
        <div class="info-group" data-title="基本信息">
          <div class="info-row">
            <span v-if="photoDetail?.attributes">
              <IconSvg
                name="resolution"
                :size="16"
                color="var(--text-secondary)"
              />
              {{ photoDetail?.attributes?.width }}x{{
                photoDetail?.attributes?.height
              }}
            </span>
            <span v-if="photoDetail?.category">
              <IconSvg
                name="category"
                :size="16"
                color="var(--text-secondary)"
              />
              {{ photoDetail.category }}
            </span>
          </div>
        </div>
        <!-- 统计信息分组 -->
        <div class="info-group" data-title="统计数据">
          <div class="info-row">
            <span v-if="photoDetail?.viewCount !== undefined">
              <IconSvg name="eye" :size="16" color="var(--text-secondary)" />
              {{ photoDetail?.viewCount || 0 }}
            </span>
            <span>
              <IconSvg
                name="download"
                :size="16"
                color="var(--text-secondary)"
              />
              {{ photoDetail?.downloadCount || 0 }}
            </span>
            <span>
              <IconSvg
                name="favorite"
                :size="16"
                color="var(--text-secondary)"
              />
              {{ photoDetail?.favoriteCount || 0 }}
            </span>
            <span>
              <IconSvg name="like" :size="16" color="var(--text-secondary)" />
              {{ photoDetail?.likeCount || 0 }}
            </span>
          </div>
        </div>
        <!-- 作者信息分组 -->
        <div
          class="info-group"
          data-title="作者信息"
          @click="navigateToUser(photoDetail?.user.uid || '')"
        >
          <div class="info-row user-row" v-if="photoDetail?.user">
            <img
              :src="photoDetail.user.avatar"
              :alt="photoDetail.user.uid"
              class="user-avatar"
            />
            <span class="username">{{ photoDetail.user.nickname }}</span>
          </div>
        </div>
        <!-- 标签分组 -->
        <div
          class="info-group tags-group"
          data-title="标签"
          v-if="photoDetail?.tags?.length"
        >
          <div class="info-row tags-row">
            <span
              class="tag"
              v-for="(tag, index) in photoDetail.tags"
              :key="index"
              @click="searchByTag(tag)"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 相关推荐部分 -->
    <div class="related-section" v-if="photoDetail">
      <h3>相关推荐</h3>
      <div class="related-container">
        <RikkaImagesWaterfallGallery
          :items="imagesArray.filter((item) => item.id !== photoDetail?.id)"
          :columns="0"
          :card-width="250"
          :gap="20"
          :loading="imagesLoading"
          :has-more="hasMoreImages"
          @load-more="handleLoadMoreImages"
          :height-limit="500"
        />
      </div>
    </div>

    <!-- 全屏预览 -->
    <div
      v-if="isFullscreen"
      class="fullscreen-overlay"
      @click="closeFullscreen"
    >
      <div class="fullscreen-container">
        <img
          :src="photoDetail?.url + '?width=1600'"
          :alt="photoDetail?.filename"
          class="fullscreen-image"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
definePageMeta({
  layout: 'details',
});

const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
// 图片详情
const photoDetail = ref<GetOtherPhotosInfo>();
const isFullscreen = ref(false);
const photoDisplayRef = ref<HTMLElement | null>(null);
const containerSize = ref({ width: 0, height: 0 });
const imageLoaded = ref(false);

const onImageLoad = () => {
  imageLoaded.value = true;
};

// 计算图片样式
const imageStyle = computed(() => {
  if (!photoDetail.value || !photoDetail.value.attributes) return {};

  const { width, height } = photoDetail.value.attributes;
  const aspectRatio = width / height;

  // 计算容器尺寸
  const containerWidth = containerSize.value.width;
  const containerHeight = containerSize.value.height;

  // 计算图片应该的尺寸
  let imgWidth, imgHeight;

  if (aspectRatio > 1) {
    // 宽图片
    imgWidth = Math.min(containerWidth, containerHeight * aspectRatio);
    imgHeight = imgWidth / aspectRatio;
  } else {
    // 高图片
    imgHeight = Math.min(containerHeight, containerWidth / aspectRatio);
    imgWidth = imgHeight * aspectRatio;
  }

  return {
    width: `${imgWidth}px`,
    height: `${imgHeight}px`,
  };
});

// 获取图片详情
const getPhotoDetail = async (id: string) => {
  try {
    imageLoaded.value = false;
    const data = await useApi().getOtherPhotoDetail(id);
    photoDetail.value = data;
    // 图片加载后更新容器尺寸
    nextTick(() => {
      updateContainerSize();
    });
  } catch (err) {
    throw err;
  }
};

// 更新容器尺寸
const updateContainerSize = () => {
  if (photoDisplayRef.value) {
    const rect = photoDisplayRef.value.getBoundingClientRect();
    containerSize.value = {
      width: rect.width - 60, // 减去内边距
      height: rect.height - 60, // 减去内边距
    };
  }
};

// 监听窗口大小变化
onMounted(() => {
  window.addEventListener('resize', updateContainerSize);
  nextTick(() => {
    updateContainerSize();
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerSize);
});

// 导航到用户页面
const navigateToUser = (uid: string) => {
  if (!uid) return;
  if (uid === authStore.authStore?.uid) {
    return router.push('/mine');
  }
  router.push({ path: '/user', query: { uid } });
};

// 按标签搜索
const searchByTag = (tag: string) => {
  router.push({ path: '/search/images', query: { key: tag } });
};

// 下载图片
const downloadImage = async () => {
  if (!photoDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '收藏功能需要登录后使用',
      type: 'info',
    });
    router.push('/auth/login');
    return;
  }

  try {
    // 开始加载动画
    useLoading().start({
      title: '下载中',
      description: '图片正在准备下载，请稍候...',
    });

    // 从服务器获取图片Blob数据
    const blob = await useApi().downloadPhoto(photoDetail.value.id);

    // 创建File对象
    const imageFile = new File(
      [blob],
      photoDetail.value.filename || 'image.png',
      { type: blob.type || 'image/png' }
    );

    // 使用工具函数下载图片到用户设备
    const { downloadImage: saveImage } = await import('@/utils/downloadImage');
    saveImage(imageFile);

    // 更新下载计数（可选，取决于API是否自动更新）
    if (photoDetail.value.downloadCount !== undefined) {
      photoDetail.value.downloadCount += 1;
    }

    // 停止加载动画
    useLoading().stop();

    // 显示下载成功消息
    useMessage({
      name: '下载成功',
      description: '图片已保存到您的设备',
      type: 'success',
    });
  } catch (error) {
    // 停止加载动画
    useLoading().stop();

    // 显示错误消息
    useMessage({
      name: '下载失败',
      description: '无法下载图片，请检查登陆状态',
      type: 'error',
    });
  }
};

// 切换收藏状态
const toggleFavorite = async () => {
  if (!photoDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '收藏功能需要登录后使用',
      type: 'info',
    });
    router.push('/auth/login');
    return;
  }

  try {
    await useApi().toggleFavoritePhoto(photoDetail.value.id);
    // 更新本地状态
    photoDetail.value.isFavorited = !photoDetail.value.isFavorited;
    photoDetail.value.favoriteCount += photoDetail.value.isFavorited ? 1 : -1;

    useMessage({
      name: photoDetail.value.isFavorited ? '收藏成功' : '取消收藏',
      description: photoDetail.value.isFavorited
        ? '已添加到收藏'
        : '已从收藏中移除',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  }
};

// 切换点赞状态
const toggleLike = async () => {
  if (!photoDetail.value) return;

  if (!authStore.isAuthenticated) {
    useMessage({
      name: '请先登录',
      description: '点赞功能需要登录后使用',
      type: 'info',
    });
    router.push('/auth/login');
    return;
  }

  try {
    await useApi().toggleLikePhoto(photoDetail.value.id);
    // 更新本地状态
    photoDetail.value.isLiked = !photoDetail.value.isLiked;
    photoDetail.value.likeCount += photoDetail.value.isLiked ? 1 : -1;

    useMessage({
      name: photoDetail.value.isLiked ? '点赞成功' : '取消点赞',
      description: '',
      type: 'success',
    });
  } catch (error) {
    useMessage({
      name: '操作失败',
      description: '请稍后再试',
      type: 'error',
    });
  }
};

// 打开全屏预览
const openFullscreen = () => {
  isFullscreen.value = true;
  // 阻止背景滚动
  document.body.style.overflow = 'hidden';
};

// 关闭全屏预览
const closeFullscreen = () => {
  isFullscreen.value = false;
  // 恢复背景滚动
  document.body.style.overflow = '';
};

/** 图片推荐模块 */
// 响应式图片数组
const imagesArray = ref<PhotosWaterfallGalleryItem[]>([]);
// 加载状态
const imagesLoading = ref(false);
// 计算属性，判断是否还有更多数据可加载
const hasMoreImages = computed(
  () => imagesPaginated.value.page < imagesPaginated.value.totalPage
);
// 分页信息
const imagesPaginated = ref<Paginated<PhotosSortField>>({
  page: 0,
  pageSize: 10,
  totalCount: 0,
  totalPage: 1,
  sortField: 'createTime',
  sortOrder: 'asc',
});

/**
 * 获取图片列表数据
 * @param query 可选查询参数
 */
const getPhotosList = async (query?: PhotosListQuery) => {
  try {
    const { list, ...res } = await useApi().getPhotosList(query);

    // 如果是第一页，清空原有数据
    if (!query || !query.page || query.page === 1) {
      imagesArray.value = [];
    }

    imagesArray.value.push(...list);
    imagesPaginated.value = res;
  } catch (err) {
    throw err;
  }
};

/**
 * 处理加载更多数据的逻辑
 */
const handleLoadMoreImages = async () => {
  if (imagesLoading.value || !hasMoreImages.value) {
    return;
  }
  imagesLoading.value = true;

  await getPhotosList({
    page: imagesPaginated.value.page + 1,
    pageSize: 20,
    sortField: 'downloadCount',
    sortOrder: 'desc',
    tags: ['', '', ...(photoDetail.value ? photoDetail.value.tags : [])],
  });
  imagesLoading.value = false;
};

// 监听路由变化，当 id 参数改变时重新加载数据
watch(
  () => route.query.id,
  async (newId) => {
    if (newId && typeof newId === 'string') {
      // 重置滚动位置到顶部
      if (import.meta.client) {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }

      // 重置相关推荐数据
      imagesArray.value = [];
      imagesPaginated.value = {
        page: 0,
        pageSize: 10,
        totalCount: 0,
        totalPage: 1,
        sortField: 'createTime',
        sortOrder: 'asc',
      };

      // 加载新的图片详情
      await getPhotoDetail(newId);

      // 确保图片详情加载完成后再加载推荐图片
      if (photoDetail.value) {
        await getPhotosList({
          page: 1,
          pageSize: 20,
          sortField: 'downloadCount',
          sortOrder: 'desc',
          tags: ['', '', ...photoDetail.value.tags],
        });
      }
    }
  },
  { immediate: true } // 立即执行一次，相当于初始化加载
);
</script>

<style lang="scss" scoped>
.photo-details {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 2rem;
  position: relative;
  padding: 1rem;

  .content-container {
    display: flex;
    gap: 1rem;
  }

  .photo-display {
    flex: 3;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow:
      0 0.5rem 2rem rgba(0, 0, 0, 0.15),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05),
      var(--shadow-neon-primary);
    height: calc(100vh - 15rem);
    min-height: 37.5rem;
    border-left: 0.3rem solid var(--interactive-primary);
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.15rem;
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--interactive-primary) 50%,
        transparent 100%
      );
      z-index: 1;
    }

    .image-wrapper {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;

      .main-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        border-radius: 1rem;
        opacity: 0;
        transition: all 0.3s ease;
        cursor: zoom-in;
        box-shadow:
          0 0.5rem 2rem rgba(0, 0, 0, 0.2),
          0 0 0 0.1rem rgba(255, 255, 255, 0.1);

        &.loaded {
          opacity: 1;
        }
      }
    }

    .image-placeholder {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      color: var(--text-secondary);
      font-size: 1.2rem;
    }

    /* 图片操作按钮样式 */
    .image-actions {
      position: absolute;
      bottom: 2.5rem;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 2rem;
      background: rgba(0, 0, 0, 0.6);
      backdrop-filter: blur(20px);
      padding: 1rem 2rem;
      border-radius: 3rem;
      box-shadow:
        0 0.5rem 2rem rgba(0, 0, 0, 0.4),
        0 0 0 0.1rem rgba(255, 255, 255, 0.1),
        var(--shadow-neon-primary);
      z-index: 10;
      border: 0.1rem solid rgba(255, 255, 255, 0.15);

      .action-button {
        width: 3.5rem;
        height: 3.5rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.15);
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 0.1rem solid rgba(255, 255, 255, 0.2);
        position: relative;

        &::before {
          content: '';
          position: absolute;
          inset: -0.1rem;
          border-radius: 50%;
          background: linear-gradient(
            45deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 50%,
            transparent 100%
          );
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          transform: scale(1.15);
          background: rgba(255, 255, 255, 0.25);
          box-shadow:
            0 0 2rem rgba(255, 255, 255, 0.4),
            0 0.5rem 1rem rgba(0, 0, 0, 0.3);

          &::before {
            opacity: 1;
          }
        }

        &:active {
          transform: scale(0.95);
        }
      }
    }
  }

  .photo-info-simple {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    gap: 1rem;
    width: 300px; /* 固定宽度 */
    min-width: 300px; /* 确保最小宽度 */
  }

  .info-group {
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 1.2rem;
    box-shadow:
      0 0.25rem 1rem rgba(0, 0, 0, 0.12),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    position: relative;
    border-left: 0.25rem solid var(--interactive-primary);
    transition: all 0.3s ease;

    &::before {
      content: attr(data-title);
      position: absolute;
      top: -0.75rem;
      left: 1.5rem;
      background: linear-gradient(
        135deg,
        var(--background-elevated) 0%,
        var(--background-floating) 100%
      );
      padding: 0.25rem 0.75rem;
      font-size: 0.85rem;
      color: var(--interactive-primary);
      border-radius: 0.5rem;
      font-weight: 600;
      border: 0.05rem solid rgba(255, 255, 255, 0.1);
      box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.1rem;
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--interactive-primary) 50%,
        transparent 100%
      );
      border-radius: 1.2rem 1.2rem 0 0;
    }
  }

  .info-row {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center; /* 居中显示 */
    gap: 1.2rem;
    font-size: 0.97rem;
    color: var(--text-secondary);
    padding: 0.3rem 0;

    span {
      display: flex;
      align-items: center;
      gap: 0.3rem;
    }
  }

  .user-row {
    gap: 0.8rem;
    cursor: pointer; /* 添加指针样式表明可点击 */
    transition: transform 0.2s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;

    &:hover {
      background: var(--background-floating);
      transform: translateY(-2px);
    }

    .user-avatar {
      width: 2.2rem;
      height: 2.2rem;
      border-radius: 50%;
      object-fit: cover;
      border: 0.15rem solid var(--border-focus-ring);
    }

    .username {
      font-weight: 600;
      color: var(--text-primary);
      font-size: 1rem;
    }
  }

  .tags-row {
    gap: 0.5rem;
    justify-content: flex-start; /* 标签左对齐 */

    .tag {
      padding: 0.3rem 0.8rem;
      background: var(--background-floating);
      border-radius: 2rem;
      font-size: 0.85rem;
      color: var(--text-secondary);
      cursor: pointer;
      transition: all 0.2s;
      margin-bottom: 0.5rem;
      border: 1px solid transparent;

      &:hover {
        background: var(--interactive-primary-translucent);
        color: var(--interactive-primary);
        transform: translateY(-0.1rem);
        border-color: var(--interactive-primary);
      }
    }
  }

  .tags-group {
    background: var(--background-secondary);
  }

  .related-section {
    background: linear-gradient(
      135deg,
      var(--background-elevated) 0%,
      var(--background-floating) 100%
    );
    border-radius: 1.5rem;
    padding: 2rem;
    box-shadow:
      0 0.5rem 2rem rgba(0, 0, 0, 0.15),
      0 0 0 0.1rem rgba(255, 255, 255, 0.05);
    border-left: 0.3rem solid var(--interactive-primary);
    position: relative;
    margin-bottom: 2rem;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 0.15rem;
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--interactive-primary) 50%,
        transparent 100%
      );
      border-radius: 1.5rem 1.5rem 0 0;
    }

    h3 {
      margin-bottom: 2rem;
      font-size: 1.5rem;
      color: var(--text-primary);
      font-weight: 600;
      position: relative;
      padding-left: 2rem;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 0.4rem;
        height: 1.5rem;
        background: linear-gradient(
          180deg,
          var(--interactive-primary) 0%,
          var(--interactive-primary-hover) 100%
        );
        border-radius: 0.2rem;
        box-shadow: 0 0 1rem var(--interactive-primary-translucent);
      }

      &::after {
        content: '';
        position: absolute;
        left: 0.6rem;
        top: 50%;
        transform: translateY(-50%);
        width: 0.15rem;
        height: 1rem;
        background: rgba(255, 255, 255, 0.4);
        border-radius: 0.075rem;
      }
    }
  }

  .fullscreen-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(0.3125rem);

    .fullscreen-container {
      position: relative;
      max-width: 95%;
      max-height: 95%;

      .fullscreen-image {
        max-width: 100%;
        max-height: 95vh;
        object-fit: contain;
        border-radius: 0.5rem;
      }
    }
  }
}

@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }

  .photo-info-simple {
    width: 100%;
    min-width: 0;
  }
}
</style>

