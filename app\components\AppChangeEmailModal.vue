<template>
  <RikkaDialog
    :show="show"
    :title="(authStore.authStore?.email ? '更换' : '绑定') + '电子邮箱'"
    :width="dialogWidth"
    :mask-closable="false"
    @close="close"
  >
    <!-- 表单主体区域 -->
    <div class="change-email-modal">
      <!-- 旧邮箱输入区域 -->
      <div class="lab" v-if="authStore.authStore?.email">
        <span> 旧邮箱: </span>
        <RikkaInput
          v-model="form.oldEmail"
          placeholder="请输入旧邮箱"
          @keyup.enter="handleConfirm"
        />
      </div>

      <!-- 新邮箱输入区域 -->
      <div class="lab">
        <span> 新邮箱: </span>
        <RikkaInput
          v-model="form.newEmail"
          placeholder="请输入新邮箱"
          @keyup.enter="handleConfirm"
        />
      </div>

      <!-- 图片验证码区域 -->
      <div class="lab">
        <span> 校验码: </span>
        <div class="captcha">
          <div class="input">
            <RikkaInput
              v-model="form.captcha"
              placeholder="请输入图片验证码"
              @keyup.enter="sendEmailCode"
            />
          </div>
          <div class="captcha-img">
            <captcha ref="captchaRef" />
          </div>
        </div>
      </div>

      <!-- 邮箱验证码区域 -->
      <div class="lab">
        <span> 验证码: </span>
        <div class="code">
          <RikkaInput
            v-model="form.code"
            placeholder="请输入邮箱码"
            @keyup.enter="handleConfirm"
          />
          <div class="btn">
            <RippleButton
              class="send"
              @click="sendEmailCode"
              :disabled="sendEmailCodeLock || countdown < 60"
            >
              {{ countdown >= 60 ? '发送验证码' : `${60 - countdown}秒后重发` }}
            </RippleButton>
          </div>
        </div>
      </div>
    </div>

    <!-- 对话框底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <RippleButton class="btn-cancel" @click="close()">取消</RippleButton>
        <RippleButton
          class="btn-confirm"
          @click="handleConfirm"
          :disabled="isDisabled"
          >确定</RippleButton
        >
      </div>
    </template>
  </RikkaDialog>
</template>

<script lang="ts" setup>
// 组件属性定义
const { show } = defineProps({
  show: Boolean, // 控制模态框显示状态
});

// 认证状态管理
const authStore = useAuthStore();
const captchaRef = ref<CaptchaExpose | null>(null); // 图片验证码组件引用

// 移动端适配 - 动态计算对话框宽度
const windowWidth = ref(0);
const dialogWidth = computed(() => {
  const isMobile = windowWidth.value <= 768;
  return isMobile ? '90vw' : '30rem';
});

// 监听窗口大小变化
const updateWindowWidth = () => {
  if (typeof window !== 'undefined') {
    windowWidth.value = window.innerWidth;
  }
};

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', updateWindowWidth);
    updateWindowWidth(); // 初始化
  }
});

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('resize', updateWindowWidth);
  }
});

// 事件发射器定义
const emit = defineEmits<{
  close: [value: boolean]; // 关闭事件，携带是否成功标志
}>();

// 关闭处理函数
const close = (flag = false) => {
  emit('close', flag);
};

// 表单数据模型
const form = ref({
  oldEmail: '', // 旧邮箱
  newEmail: '', // 新邮箱
  captcha: '', // 图片验证码
  code: '', // 邮箱验证码
});

// 验证码发送相关状态
const { setEmailDate, getCodeDate } = useSendCodeStore();
const countdown = ref(0); // 倒计时秒数
const sendEmailCodeLock = ref(false); // 发送按钮锁定状态

// 倒计时处理逻辑
const startCountdown = () => {
  countdown.value = Math.floor(
    (new Date().getTime() - new Date(getCodeDate().email).getTime()) / 1000
  );
  if (countdown.value >= 60) return;
  const timer = setInterval(() => {
    if (countdown.value > 60) {
      clearInterval(timer);
    }

    countdown.value = Math.floor(
      (new Date().getTime() - new Date(getCodeDate().email).getTime()) / 1000
    );
  }, 1000);
};
startCountdown();

// 发送邮箱验证码函数（带防抖）
const sendEmailCode = useDebounceFn(async () => {
  if (authStore.authStore?.email !== form.value.oldEmail) {
    return useMessage({
      name: '请重新输入旧邮箱',
      description: '旧邮箱错误',
      type: 'error',
    });
  }

  // 表单验证
  if (!form.value.newEmail) {
    return useMessage({
      name: '新邮箱未输入',
      description: '新邮箱必须输入',
      type: 'error',
    });
  } else {
    const emailReg =
      /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (!emailReg.test(form.value.newEmail)) {
      return useMessage({
        name: '邮箱格式错误',
        description: '邮箱格式错误',
        type: 'error',
      });
    }
  }
  if (!form.value.captcha) {
    return useMessage({
      name: '验证码未输入',
      description: '验证码必须输入',
      type: 'error',
    });
  }
  try {
    sendEmailCodeLock.value = true;
    await useApi().sendEmailCaptcha({
      email: form.value.newEmail,
      code: form.value.captcha,
    });
    sendEmailCodeLock.value = false;
    // 发送成功开始倒计时
    setEmailDate();
    startCountdown();
  } catch (err) {
    captchaRef.value?.refresh();
    sendEmailCodeLock.value = false;
  }
}, 300);

// 表单提交禁用状态计算
const isDisabled = computed(() => {
  return (
    !form.value.oldEmail ||
    !form.value.newEmail ||
    !form.value.captcha ||
    !form.value.code
  );
});

// 确认修改处理函数
const handleConfirm = async () => {
  if (isDisabled.value) return;
  try {
    await useApi().changeEmail({
      oldEmail: form.value.oldEmail,
      email: form.value.newEmail,
      code: form.value.code,
    });
    useMessage({
      name: '修改成功',
      description: '修改成功，请重新登录',
      type: 'success',
    });
    await useApi().logout();
    close(true);
  } catch (err) {}
};
</script>

<!-- 样式部分保持原有结构 -->
<style lang="scss" scoped>
.lab {
  display: flex;
  align-items: center;
  margin-bottom: 0.3rem;

  > span {
    margin-right: 1rem;
  }

  > div {
    flex: 1;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.captcha {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.3rem;

  > .input {
    flex: 1;
  }

  > .captcha-img {
    display: flex;
    height: 2.7rem;
    margin-left: 0.5rem;

    > .captcha-img {
      margin-left: 0.5rem;
    }
  }
}
.code {
  display: flex;
  align-items: center;

  > .btn {
    margin-left: 0.5rem;

    > .send {
      margin: 0 auto;
      width: 7.8rem;
      height: 2.7rem;
      background-color: var(--button-primary);
      color: var(--text-primary);

      transition: all 0.3s ease-in-out;

      &:hover {
        background-color: var(--button-primary-hover);
      }
    }
  }
}

.dialog-footer {
  flex: 1;
  display: flex;
  justify-content: space-evenly;

  > .btn-cancel {
    width: 8rem;
    background-color: var(--button-cancel);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-cancel-hover);
    }
  }

  > .btn-confirm {
    width: 8rem;
    background-color: var(--button-primary);
    color: var(--text-primary);
    transition: all 0.3s ease-in-out;

    &:hover {
      background-color: var(--button-primary-hover);
    }
  }
}
</style>

